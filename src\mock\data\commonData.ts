import {
    AUTH_PREFIX_PATH,
    UI_COMPONENTS_PREFIX_PATH,
    DASHBOARDS_PREFIX_PATH,
    CONCEPTS_PREFIX_PATH,
    GUIDE_PREFIX_PATH,
} from '@/constants/route.constant'

export const notificationListData = [
    {
        id: 'e55adc24-1803-4ffd-b653-09be273f8df5',
        target: '<PERSON>',
        description: 'mentioned your in comment.',
        date: '2 minutes ago',
        image: 'thumb-2.jpg',
        type: 0,
        location: '',
        locationLabel: '',
        status: '',
        readed: true,
    },
    {
        id: 'b06ca3f5-8fb0-4979-a016-30dfe63e8fd6',
        target: '<PERSON>',
        description: 'invited you to new project.',
        date: '10 minutes ago',
        image: 'thumb-3.jpg',
        type: 0,
        location: '',
        locationLabel: '',
        status: '',
        readed: false,
    },
    {
        id: 'f644235d-dffc-4f17-883f-1ada117ff2c9',
        target: '',
        description: 'Please submit your daily report.',
        date: '3 hours ago',
        image: '',
        type: 1,
        location: '',
        locationLabel: '',
        status: '',
        readed: false,
    },
    {
        id: '2152cd09-413a-44be-9d5a-b2b820c6a661',
        target: 'Shannon Baker',
        description: 'comment in your ticket.',
        date: '4 hours ago',
        image: 'thumb-4.jpg',
        type: 0,
        location: '',
        locationLabel: '',
        status: '',
        readed: false,
    },
    {
        id: '8ca04d2c-0262-417b-8a3d-4ade49939059',
        target: '',
        description: 'Your request was rejected',
        date: '2 days ago',
        image: '',
        type: 2,
        location: '',
        locationLabel: '',
        status: 'failed',
        readed: true,
    },

    {
        id: '8dd23dfd-a79b-40ad-b4e9-7e70a148d5b6',
        target: '',
        description: 'Your request has been approved.',
        date: '4 minutes ago',
        image: '4 days ago',
        type: 2,
        location: '',
        locationLabel: '',
        status: 'succeed',
        readed: true,
    },
]

export const searchQueryPoolData = [
    {
        key: 'dashboard.ecommerce',
        path: `${DASHBOARDS_PREFIX_PATH}/ecommerce`,
        title: 'Ecommerce',
        icon: 'dashboardEcommerce',
        category: 'Dashboard',
        categoryTitle: 'Dashboard',
    },
    {
        key: 'dashboard.project',
        path: `${DASHBOARDS_PREFIX_PATH}/project`,
        title: 'Project',
        icon: 'dashboardProject',
        category: 'Dashboard',
        categoryTitle: 'Dashboard',
    },
    {
        key: 'dashboard.marketing',
        path: `${DASHBOARDS_PREFIX_PATH}/marketing`,
        title: 'Marketing',
        icon: 'dashboardMarketing',
        category: 'Dashboard',
        categoryTitle: 'Dashboard',
    },
    {
        key: 'dashboard.analytic',
        path: `${DASHBOARDS_PREFIX_PATH}/analytic`,
        title: 'Analytic',
        icon: 'dashboardAnalytic',
        category: 'Dashboard',
        categoryTitle: 'Dashboard',
    },
    {
        key: 'authentication.signInSimple',
        path: `${AUTH_PREFIX_PATH}/sign-in-simple`,
        title: 'Sign In Simple',
        icon: 'signIn',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.signInSide',
        path: `${AUTH_PREFIX_PATH}/sign-in-side`,
        title: 'Sign In Side',
        icon: 'signIn',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.signInSplit',
        path: `${AUTH_PREFIX_PATH}/sign-in-split`,
        title: 'Sign In Split',
        icon: 'signIn',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.signUpSimple',
        path: `${AUTH_PREFIX_PATH}/sign-up-simple`,
        title: 'Sign Up Simple',
        icon: 'signUp',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.signUpSide',
        path: `${AUTH_PREFIX_PATH}/sign-up-side`,
        title: 'Sign Up Side',
        icon: 'signUp',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.signUpSplit',
        path: `${AUTH_PREFIX_PATH}/sign-up-split`,
        title: 'Sign Up PasswordSplit',
        icon: 'signUp',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.forgotPasswordSimple',
        path: `${AUTH_PREFIX_PATH}/forgot-password-simple`,
        title: 'Forget PasswordSimple',
        icon: 'forgotPassword',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.forgotPasswordSide',
        path: `${AUTH_PREFIX_PATH}/forgot-password-side`,
        title: 'Forget Password Side',
        icon: 'forgotPassword',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.forgotPasswordSplit',
        path: `${AUTH_PREFIX_PATH}/forgot-password-split`,
        title: 'Forget Password Split',
        icon: 'forgotPassword',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.resetPasswordSimple',
        path: `${AUTH_PREFIX_PATH}/reset-password-simple`,
        title: 'Reset Password Simple',
        icon: 'resetPassword',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.resetPasswordSide',
        path: `${AUTH_PREFIX_PATH}/reset-password-side`,
        title: 'Reset Password Side',
        icon: 'resetPassword',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'authentication.resetPasswordSplit',
        path: `${AUTH_PREFIX_PATH}/reset-password-split`,
        title: 'Split',
        icon: 'resetPassword',
        category: 'Authentication',
        categoryTitle: 'Auth',
    },
    {
        key: 'uiComponent.common.button',
        path: `${UI_COMPONENTS_PREFIX_PATH}/button`,
        title: 'Button',
        icon: 'uiCommonButton',
        category: 'Common',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.common.grid',
        path: `${UI_COMPONENTS_PREFIX_PATH}/grid`,
        title: 'Grid',
        icon: 'uiCommonGrid',
        category: 'Common',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.common.typography',
        path: `${UI_COMPONENTS_PREFIX_PATH}/typography`,
        title: 'Typography',
        icon: 'uiCommonTypography',
        category: 'Common',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.common.icons',
        path: `${UI_COMPONENTS_PREFIX_PATH}/icons`,
        title: 'Icons',
        icon: 'uiCommonIcons',
        category: 'Common',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.feedback.alert',
        path: `${UI_COMPONENTS_PREFIX_PATH}/alert`,
        title: 'Alert',
        icon: 'uiFeedbackAlert',
        category: 'Feedback',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.feedback.dialog',
        path: `${UI_COMPONENTS_PREFIX_PATH}/dialog`,
        title: 'Dialog',
        icon: 'uiFeedbackDialog',
        category: 'Feedback',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.feedback.drawer',
        path: `${UI_COMPONENTS_PREFIX_PATH}/drawer`,
        title: 'Drawer',
        icon: 'uiFeedbackDrawer',
        category: 'Feedback',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.feedback.progress',
        path: `${UI_COMPONENTS_PREFIX_PATH}/progress`,
        title: 'Progress',
        icon: 'uiFeedbackProgress',
        category: 'Feedback',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.feedback.skeleton',
        path: `${UI_COMPONENTS_PREFIX_PATH}/skeleton`,
        title: 'Skeleton',
        icon: 'uiFeedbackSkeleton',
        category: 'Feedback',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.feedback.spinner',
        path: `${UI_COMPONENTS_PREFIX_PATH}/spinner`,
        title: 'Spinner',
        icon: 'uiFeedbackSpinner',
        category: 'Feedback',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.feedback.toast',
        path: `${UI_COMPONENTS_PREFIX_PATH}/toast`,
        title: 'Toast',
        icon: 'uiFeedbackToast',
        category: 'Feedback',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.avatar',
        path: `${UI_COMPONENTS_PREFIX_PATH}/avatar`,
        title: 'Avatar',
        icon: 'uiDataDisplayAvatar',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.badge',
        path: `${UI_COMPONENTS_PREFIX_PATH}/badge`,
        title: 'Badge',
        icon: 'uiDataDisplayBadge',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.calendar',
        path: `${UI_COMPONENTS_PREFIX_PATH}/calendar`,
        title: 'Calendar',
        icon: 'uiDataDisplayCalendar',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.cards',
        path: `${UI_COMPONENTS_PREFIX_PATH}/cards`,
        title: 'Cards',
        icon: 'uiDataDisplayCard',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.table',
        path: `${UI_COMPONENTS_PREFIX_PATH}/table`,
        title: 'Table',
        icon: 'uiDataDisplayTable',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.tag',
        path: `${UI_COMPONENTS_PREFIX_PATH}/tag`,
        title: 'Tag',
        icon: 'uiDataDisplayTag',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.timeline',
        path: `${UI_COMPONENTS_PREFIX_PATH}/timeline`,
        title: 'Timeline',
        icon: 'uiDataDisplayTimeline',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.dataDisplay.tooltip',
        path: `${UI_COMPONENTS_PREFIX_PATH}/tooltip`,
        title: 'Tooltip',
        icon: 'uiDataDisplayTooltip',
        category: 'Data Display',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.checkbox',
        path: `${UI_COMPONENTS_PREFIX_PATH}/checkbox`,
        title: 'Checkbox',
        icon: 'uiFormsCheckbox',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.datePicker',
        path: `${UI_COMPONENTS_PREFIX_PATH}/date-picker`,
        title: 'Date picker',
        icon: 'uiFormsDatepicker',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.formControl',
        path: `${UI_COMPONENTS_PREFIX_PATH}/form-control`,
        title: 'Form control',
        icon: 'uiFormsFormControl',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.input',
        path: `${UI_COMPONENTS_PREFIX_PATH}/input`,
        title: 'Input',
        icon: 'uiFormsInput',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.inputGroup',
        path: `${UI_COMPONENTS_PREFIX_PATH}/input-group`,
        title: 'Input Group',
        icon: 'uiFormsInputGroup',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.radio',
        path: `${UI_COMPONENTS_PREFIX_PATH}/radio`,
        title: 'Radio',
        icon: 'uiFormsRadio',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.segment',
        path: `${UI_COMPONENTS_PREFIX_PATH}/segment`,
        title: 'Segment',
        icon: 'uiFormsSegment',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.select',
        path: `${UI_COMPONENTS_PREFIX_PATH}/select`,
        title: 'Select',
        icon: 'uiFormsSelect',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.switcher',
        path: `${UI_COMPONENTS_PREFIX_PATH}/switcher`,
        title: 'Switcher',
        icon: 'uiFormsSwitcher',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.timeInput',
        path: `${UI_COMPONENTS_PREFIX_PATH}/time-input`,
        title: 'Time Input',
        icon: 'uiFormsTimePicker',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.forms.upload',
        path: `${UI_COMPONENTS_PREFIX_PATH}/upload`,
        title: 'Upload',
        icon: 'uiFormsUpload',
        category: 'Forms',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.navigation.dropdown',
        path: `${UI_COMPONENTS_PREFIX_PATH}/dropdown`,
        title: 'Dropdown',
        icon: 'uiNavigationDropdown',
        category: 'Navigation',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.navigation.menu',
        path: `${UI_COMPONENTS_PREFIX_PATH}/menu`,
        title: 'Menu',
        icon: 'uiNavigationMenu',
        category: 'Navigation',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.navigation.pagination',
        path: `${UI_COMPONENTS_PREFIX_PATH}/pagination`,
        title: 'Pagination',
        icon: 'uiNavigationPagination',
        category: 'Navigation',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.navigation.steps',
        path: `${UI_COMPONENTS_PREFIX_PATH}/steps`,
        title: 'Steps',
        icon: 'uiNavigationSteps',
        category: 'Navigation',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.navigation.tabs',
        path: `${UI_COMPONENTS_PREFIX_PATH}/tabs`,
        title: 'Tabs',
        icon: 'uiNavigationTabs',
        category: 'Navigation',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.graph.charts',
        path: `${UI_COMPONENTS_PREFIX_PATH}/graph/charts`,
        title: 'Charts',
        icon: 'uiGraphChart',
        category: 'Graph',
        categoryTitle: 'UI Components',
    },
    {
        key: 'uiComponent.graph.maps',
        path: `${UI_COMPONENTS_PREFIX_PATH}/graph/maps`,
        title: 'Maps',
        icon: 'uiGraphMaps',
        category: 'Graph',
        categoryTitle: 'UI Components',
    },
    {
        key: 'concepts.ai.chat',
        path: `${CONCEPTS_PREFIX_PATH}/ai/chat`,
        title: 'Chat',
        icon: 'aiChat',
        category: 'AI',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.ai.image',
        path: `${CONCEPTS_PREFIX_PATH}/ai/image`,
        title: 'Image',
        icon: 'aiImage',
        category: 'AI',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.projects.scrumBoard',
        path: `${CONCEPTS_PREFIX_PATH}/projects/scrum-board`,
        title: 'Scrum Board',
        icon: 'projectScrumBoard',
        category: 'Projects',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.projects.projectList',
        path: `${CONCEPTS_PREFIX_PATH}/projects/project-list`,
        title: 'Project List',
        icon: 'projectList',
        category: 'Projects',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.projects.projectDetails',
        path: `${CONCEPTS_PREFIX_PATH}/projects/project-details/27`,
        title: 'Details',
        icon: 'projectDetails',
        category: 'Projects',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.projects.projectTasks',
        path: `${CONCEPTS_PREFIX_PATH}/projects/tasks`,
        title: 'Tasks',
        icon: 'projectTask',
        category: 'Projects',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.projects.projectIssue',
        path: `${CONCEPTS_PREFIX_PATH}/projects/tasks/1`,
        title: 'Issue',
        icon: 'projectIssue',
        category: 'Projects',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.customers.customerList',
        path: `${CONCEPTS_PREFIX_PATH}/customers/customer-list`,
        title: 'Customer List',
        icon: 'customerList',
        category: 'Customers',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.customers.customerEdit',
        path: `${CONCEPTS_PREFIX_PATH}/customers/customer-edit/1`,
        title: 'Customer Edit',
        icon: 'customerEdit',
        category: 'Customers',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.customers.customerCreate',
        path: `${CONCEPTS_PREFIX_PATH}/customers/customer-create`,
        title: 'Customer Create',
        icon: 'customerCreate',
        category: 'Customers',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.customers.customerDetails',
        path: `${CONCEPTS_PREFIX_PATH}/customers/customer-details/1`,
        title: 'Customer Details',
        icon: 'customerDetails',
        category: 'Customers',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.products.productList',
        path: `${CONCEPTS_PREFIX_PATH}/products/product-list`,
        title: 'Product List',
        icon: 'productList',
        category: 'Products',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.products.productEdit',
        path: `${CONCEPTS_PREFIX_PATH}/products/product-edit/12`,
        title: 'Product Edit',
        icon: 'productEdit',
        category: 'Products',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.products.productCreate',
        path: `${CONCEPTS_PREFIX_PATH}/products/product-create`,
        title: 'Product Create',
        icon: 'productCreate',
        category: 'Products',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.orders.orderList',
        path: `${CONCEPTS_PREFIX_PATH}/orders/order-list`,
        title: 'Order List',
        icon: 'orderList',
        category: 'Orders',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.orders.orderEdit',
        path: `${CONCEPTS_PREFIX_PATH}/orders/order-edit/95954`,
        title: 'Order Edit',
        icon: 'orderEdit',
        category: 'Orders',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.orders.orderCreate',
        path: `${CONCEPTS_PREFIX_PATH}/orders/order-create`,
        title: 'Order Create',
        icon: 'orderCreate',
        category: 'Orders',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.orders.orderDetails',
        path: `${CONCEPTS_PREFIX_PATH}/orders/order-details/95954`,
        title: 'Order Details',
        icon: 'ordeDetails',
        category: 'Orders',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.account.settings',
        path: `${CONCEPTS_PREFIX_PATH}/account/settings`,
        title: 'Settings',
        icon: 'accountSettings',
        category: 'Account',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.account.activityLog',
        path: `${CONCEPTS_PREFIX_PATH}/account/activity-log`,
        title: 'Activity log',
        icon: 'accountActivityLogs',
        category: 'Account',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.account.rolesPermissions',
        path: `${CONCEPTS_PREFIX_PATH}/account/roles-permissions`,
        title: 'Roles & Permissions',
        icon: 'accountRoleAndPermission',
        category: 'Account',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.account.pricing',
        path: `${CONCEPTS_PREFIX_PATH}/account/pricing`,
        title: 'Pricing',
        icon: 'accountPricing',
        category: 'Account',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.helpCenter.supportHub',
        path: `${CONCEPTS_PREFIX_PATH}/help-center/support-hub`,
        title: 'Support Hub',
        icon: 'helpCeterSupportHub',
        category: 'Help Center',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.helpCenter.article',
        path: `${CONCEPTS_PREFIX_PATH}/help-center/article/pWBKE_0UiQ`,
        title: 'Article',
        icon: 'helpCeterArticle',
        category: 'Help Center',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.helpCenter.editArticle',
        path: `${CONCEPTS_PREFIX_PATH}/help-center/edit-article/pWBKE_0UiQ`,
        title: 'Edit Article',
        icon: 'helpCeterEditArticle',
        category: 'Help Center',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.helpCenter.manageArticle',
        path: `${CONCEPTS_PREFIX_PATH}/help-center/manage-article`,
        title: 'Manage Article',
        icon: 'helpCeterManageArticle',
        category: 'Help Center',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.calendar',
        path: `${CONCEPTS_PREFIX_PATH}/calendar`,
        title: 'Calendar',
        icon: 'calendar',
        category: 'Others',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.fileManager',
        path: `${CONCEPTS_PREFIX_PATH}/file-manager`,
        title: 'File Manager',
        icon: 'fileManager',
        category: 'Others',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.mail',
        path: `${CONCEPTS_PREFIX_PATH}/mail`,
        title: 'Mail',
        icon: 'mail',
        category: 'Others',
        categoryTitle: 'Concepts',
    },
    {
        key: 'concepts.chat',
        path: `${CONCEPTS_PREFIX_PATH}/chat`,
        title: 'Chat',
        icon: 'chat',
        category: 'Others',
        categoryTitle: 'Concepts',
    },
    {
        key: 'documentation.introduction',
        path: `${GUIDE_PREFIX_PATH}/documentation/introduction`,
        title: 'Introduction',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.installation',
        path: `${GUIDE_PREFIX_PATH}/documentation/installation`,
        title: 'Installation',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.tailwindcss',
        path: `${GUIDE_PREFIX_PATH}/documentation/tailwindcss`,
        title: 'TailwindCSS',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.css',
        path: `${GUIDE_PREFIX_PATH}/documentation/css`,
        title: 'CSS',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.starter',
        path: `${GUIDE_PREFIX_PATH}/documentation/starter`,
        title: 'Starter',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.typescript',
        path: `${GUIDE_PREFIX_PATH}/documentation/typescript`,
        title: 'Typescript',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.updating',
        path: `${GUIDE_PREFIX_PATH}/documentation/updating`,
        title: 'Updating',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.development-server',
        path: `${GUIDE_PREFIX_PATH}/documentation/development-server`,
        title: 'Development Server',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.folder-structure',
        path: `${GUIDE_PREFIX_PATH}/documentation/folder-structure`,
        title: 'Folder Structure',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.routing',
        path: `${GUIDE_PREFIX_PATH}/documentation/routing`,
        title: 'Routing',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.stateManagement',
        path: `${GUIDE_PREFIX_PATH}/documentation/stateManagement`,
        title: 'State management',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.api-integration',
        path: `${GUIDE_PREFIX_PATH}/documentation/api-integration`,
        title: 'API Integration',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.authentication',
        path: `${GUIDE_PREFIX_PATH}/documentation/authentication`,
        title: 'Authentication',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.mock-api',
        path: `${GUIDE_PREFIX_PATH}/documentation/mock-api`,
        title: 'Mock Api',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.firebase',
        path: `${GUIDE_PREFIX_PATH}/documentation/firebase`,
        title: 'Firebase',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.app',
        path: `${GUIDE_PREFIX_PATH}/documentation/app`,
        title: 'App Config',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.layouts',
        path: `${GUIDE_PREFIX_PATH}/documentation/layouts`,
        title: 'Layouts',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.navigation-config',
        path: `${GUIDE_PREFIX_PATH}/documentation/navigation-config`,
        title: 'Navigation Config',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.theming',
        path: `${GUIDE_PREFIX_PATH}/documentation/theming`,
        title: 'Theming',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.internationalization',
        path: `${GUIDE_PREFIX_PATH}/documentation/internationalization`,
        title: 'Internationalization',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.dark-light-mode',
        path: `${GUIDE_PREFIX_PATH}/documentation/dark-light-mode`,
        title: 'Dark/Light Mode',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.direction',
        path: `${GUIDE_PREFIX_PATH}/documentation/direction`,
        title: 'Direction',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.overall-theme-config',
        path: `${GUIDE_PREFIX_PATH}/documentation/overall-theme-config`,
        title: 'Overall Theme Config',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.build-production',
        path: `${GUIDE_PREFIX_PATH}/documentation/build-production`,
        title: 'Build production',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'documentation.credit',
        path: `${GUIDE_PREFIX_PATH}/documentation/credit`,
        title: 'Sources & Credits',
        icon: 'documentation',
        category: 'Documentation',
        categoryTitle: 'Guide',
    },
    {
        key: 'guide.documentation',
        path: `${GUIDE_PREFIX_PATH}/documentation/introduction`,
        title: 'Documentation',
        icon: 'documentation',
        category: 'Docs',
        categoryTitle: 'Guide',
    },
    {
        key: 'guide.sharedComponentDoc',
        path: `${GUIDE_PREFIX_PATH}/shared-component-doc/abbreviate-number`,
        title: 'Shared Component',
        icon: 'sharedComponentDoc',
        category: 'Docs',
        categoryTitle: 'Guide',
    },
    {
        key: 'guide.utilsDoc',
        path: `${GUIDE_PREFIX_PATH}/utils-doc/use-auth`,
        title: 'Utilities',
        icon: 'utilsDoc',
        category: 'Docs',
        categoryTitle: 'Guide',
    },
    {
        key: 'guide.changeLog',
        path: `${GUIDE_PREFIX_PATH}/changelog`,
        title: 'Changelog',
        icon: 'changeLog',
        category: 'Docs',
        categoryTitle: 'Guide',
    },
]
