'use client'

import Card from '@/components/ui/Card'
import { TbTrendingUp, TbTrendingDown, TbCurrencyDollar, TbTarget, TbUsers, TbChartLine } from 'react-icons/tb'

interface KpiSummaryProps {
    data: {
        totalMarketingSpend: {
            value: number
            growShrink: number
        }
        roi: {
            value: number
            growShrink: number
        }
        conversionRates: {
            value: number
            growShrink: number
        }
        totalLeads: {
            value: number
            growShrink: number
        }
    }
}

const KpiSummary = ({ data }: KpiSummaryProps) => {
    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value)
    }

    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('en-US').format(value)
    }

    const renderGrowthIndicator = (growShrink: number) => {
        const isPositive = growShrink > 0
        return (
            <div className={`flex items-center gap-1 ${isPositive ? 'text-emerald-500' : 'text-red-500'}`}>
                {isPositive ? <TbTrendingUp className="w-4 h-4" /> : <TbTrendingDown className="w-4 h-4" />}
                <span className="text-sm font-medium">{Math.abs(growShrink)}%</span>
            </div>
        )
    }

    const kpis = [
        {
            title: 'Marketing Spend',
            value: formatCurrency(data?.totalMarketingSpend?.value || 0),
            growth: data?.totalMarketingSpend?.growShrink || 0,
            icon: <TbCurrencyDollar className="w-6 h-6" />,
            color: 'bg-blue-500'
        },
        {
            title: 'ROI',
            value: `${data?.roi?.value || 0}%`,
            growth: data?.roi?.growShrink || 0,
            icon: <TbChartLine className="w-6 h-6" />,
            color: 'bg-emerald-500'
        },
        {
            title: 'Conversion Rate',
            value: `${data?.conversionRates?.value || 0}%`,
            growth: data?.conversionRates?.growShrink || 0,
            icon: <TbTarget className="w-6 h-6" />,
            color: 'bg-amber-500'
        },
        {
            title: 'Total Leads',
            value: formatNumber(data?.totalLeads?.value || 0),
            growth: data?.totalLeads?.growShrink || 0,
            icon: <TbUsers className="w-6 h-6" />,
            color: 'bg-purple-500'
        }
    ]

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
            {kpis.map((kpi, index) => (
                <Card key={index}>
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <div className="text-sm text-gray-500 mb-1">
                                {kpi.title}
                            </div>
                            <div className="text-2xl font-bold mb-2">
                                {kpi.value}
                            </div>
                            {renderGrowthIndicator(kpi.growth)}
                        </div>
                        <div className={`p-3 rounded-lg ${kpi.color} text-white`}>
                            {kpi.icon}
                        </div>
                    </div>
                </Card>
            ))}
        </div>
    )
}

export default KpiSummary
