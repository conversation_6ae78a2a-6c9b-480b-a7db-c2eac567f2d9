'use client'

import Card from '@/components/ui/Card'
import { TbCalendar, TbClock } from 'react-icons/tb'

const UpcomingSchedule = () => {
    const upcomingEvents = [
        {
            id: 1,
            title: 'Team Standup',
            time: '09:00 AM',
            date: 'Today',
            type: 'meeting'
        },
        {
            id: 2,
            title: 'Project Review',
            time: '02:00 PM',
            date: 'Today',
            type: 'review'
        },
        {
            id: 3,
            title: 'Client Presentation',
            time: '10:00 AM',
            date: 'Tomorrow',
            type: 'presentation'
        },
        {
            id: 4,
            title: 'Sprint Planning',
            time: '03:00 PM',
            date: 'Dec 20',
            type: 'planning'
        }
    ]

    const getTypeColor = (type: string) => {
        switch (type) {
            case 'meeting':
                return 'bg-blue-100 text-blue-600'
            case 'review':
                return 'bg-emerald-100 text-emerald-600'
            case 'presentation':
                return 'bg-purple-100 text-purple-600'
            case 'planning':
                return 'bg-amber-100 text-amber-600'
            default:
                return 'bg-gray-100 text-gray-600'
        }
    }

    return (
        <Card>
            <div className="flex items-center gap-2 mb-6">
                <TbCalendar className="w-5 h-5 text-primary" />
                <h5>Upcoming Schedule</h5>
            </div>
            
            <div className="space-y-4">
                {upcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-start gap-3">
                        <div className={`p-2 rounded-lg ${getTypeColor(event.type)}`}>
                            <TbClock className="w-4 h-4" />
                        </div>
                        <div className="flex-1">
                            <div className="font-medium text-sm mb-1">
                                {event.title}
                            </div>
                            <div className="text-xs text-gray-500">
                                {event.date} at {event.time}
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button className="text-sm text-primary hover:text-primary-dark font-medium">
                    View all events
                </button>
            </div>
        </Card>
    )
}

export default UpcomingSchedule
