import { NextResponse } from 'next/server'
import { isSupabaseConfigured } from '@/lib/supabase'

export async function GET() {
    try {
        const configured = isSupabaseConfigured()
        
        return NextResponse.json({
            supabaseConfigured: configured,
            url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
            anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
            serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
            message: configured 
                ? 'Supabase is properly configured!' 
                : 'Supabase is not configured. Please set your environment variables.'
        })
    } catch (error) {
        console.error('Test endpoint error:', error)
        return NextResponse.json({ 
            error: 'Failed to test configuration',
            supabaseConfigured: false 
        }, { status: 500 })
    }
}
