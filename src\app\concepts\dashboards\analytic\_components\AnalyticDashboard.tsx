'use client'

import Card from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'

interface AnalyticDashboardProps {
    data: {
        totalUsers?: number
        totalSessions?: number
        bounceRate?: string
    }
}

const AnalyticDashboard = ({ data }: AnalyticDashboardProps) => {
    return (
        <div className="flex flex-col gap-4">
            <Card>
                <h4 className="mb-4">Analytics Overview</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="text-center">
                        <div className="text-2xl font-bold text-primary">
                            {data?.totalUsers || '12,345'}
                        </div>
                        <div className="text-sm text-gray-500">Total Users</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-emerald-500">
                            {data?.totalSessions || '8,921'}
                        </div>
                        <div className="text-sm text-gray-500">Total Sessions</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-blue-500">
                            {data?.bounceRate || '32.5%'}
                        </div>
                        <div className="text-sm text-gray-500">Bounce Rate</div>
                    </div>
                </div>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                    <h5 className="mb-4">User Activity</h5>
                    <Chart
                        type="line"
                        height={300}
                        series={[
                            {
                                name: 'Users',
                                data: [120, 132, 101, 134, 90, 230, 210, 320, 290, 250, 280, 300]
                            }
                        ]}
                        xAxis={['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']}
                    />
                </Card>

                <Card>
                    <h5 className="mb-4">Traffic Sources</h5>
                    <Chart
                        type="donut"
                        height={300}
                        series={[44, 55, 13, 43]}
                        customOptions={{
                            labels: ['Organic Search', 'Direct', 'Social Media', 'Referral'],
                            colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444']
                        }}
                    />
                </Card>
            </div>

            <Card>
                <h5 className="mb-4">Page Views Trend</h5>
                <Chart
                    type="area"
                    height={350}
                    series={[
                        {
                            name: 'Page Views',
                            data: [1200, 1350, 1100, 1400, 1000, 1800, 1600, 2100, 1900, 1700, 1950, 2200]
                        }
                    ]}
                    xAxis={['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']}
                />
            </Card>
        </div>
    )
}

export default AnalyticDashboard
