'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'
import Segment from '@/components/ui/Segment'

interface SalesTargetProps {
    data: {
        thisWeek: {
            target: number
            achieved: number
            percentage: number
        }
        thisMonth: {
            target: number
            achieved: number
            percentage: number
        }
        thisYear: {
            target: number
            achieved: number
            percentage: number
        }
    }
}

const SalesTarget = ({ data }: SalesTargetProps) => {
    const [selectedPeriod, setSelectedPeriod] = useState('thisWeek')

    const periodOptions = [
        { label: 'Week', value: 'thisWeek' },
        { label: 'Month', value: 'thisMonth' },
        { label: 'Year', value: 'thisYear' }
    ]

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value)
    }

    const currentData = data?.[selectedPeriod as keyof typeof data] || {
        target: 0,
        achieved: 0,
        percentage: 0
    }

    const getProgressColor = (percentage: number) => {
        if (percentage >= 80) return 'bg-emerald-500'
        if (percentage >= 60) return 'bg-amber-500'
        return 'bg-red-500'
    }

    const getProgressTextColor = (percentage: number) => {
        if (percentage >= 80) return 'text-emerald-600'
        if (percentage >= 60) return 'text-amber-600'
        return 'text-red-600'
    }

    return (
        <Card>
            <div className="flex items-center justify-between mb-6">
                <h5>Sales Target</h5>
                <Segment
                    value={selectedPeriod}
                    onChange={(value) => setSelectedPeriod(value as string)}
                    size="sm"
                >
                    {periodOptions.map(option => (
                        <Segment.Item key={option.value} value={option.value}>
                            {option.label}
                        </Segment.Item>
                    ))}
                </Segment>
            </div>

            <div className="text-center mb-6">
                <Progress
                    percent={currentData.percentage}
                    variant="circle"
                    width={120}
                    strokeWidth={8}
                    customColorClass={getProgressColor(currentData.percentage)}
                    customInfo={
                        <div className="text-center">
                            <div className={`text-lg font-bold ${getProgressTextColor(currentData.percentage)}`}>
                                {currentData.percentage}%
                            </div>
                            <div className="text-xs text-gray-500">Complete</div>
                        </div>
                    }
                />
            </div>

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Target</span>
                    <span className="font-semibold">
                        {formatCurrency(currentData.target)}
                    </span>
                </div>
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Achieved</span>
                    <span className="font-semibold text-emerald-600">
                        {formatCurrency(currentData.achieved)}
                    </span>
                </div>
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Remaining</span>
                    <span className="font-semibold text-red-600">
                        {formatCurrency(currentData.target - currentData.achieved)}
                    </span>
                </div>
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500 text-center">
                    {selectedPeriod === 'thisWeek' && 'This week\'s performance'}
                    {selectedPeriod === 'thisMonth' && 'This month\'s performance'}
                    {selectedPeriod === 'thisYear' && 'This year\'s performance'}
                </div>
            </div>
        </Card>
    )
}

export default SalesTarget
