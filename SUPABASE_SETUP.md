# Supabase Authentication Setup Guide

This guide will help you set up Supabase authentication for your CheckGap application.

## 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com) and create a new account or sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: `checkgap` (or your preferred name)
   - Database Password: Generate a strong password
   - Region: Choose the closest region to your users
5. Click "Create new project"

## 2. Get Your Project Credentials

Once your project is created:

1. Go to Settings → API
2. Copy the following values:
   - **Project URL** (under Project URL)
   - **anon public key** (under Project API keys)
   - **service_role key** (under Project API keys) - Keep this secret!

## 3. Update Environment Variables

Update your `.env` file with your Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

Replace the placeholder values with your actual Supabase credentials.

## 4. Configure Authentication Settings

1. In your Supabase dashboard, go to Authentication → Settings
2. Configure the following:

### Site URL
- Set your Site URL to: `http://localhost:3000` (for development)
- For production, use your actual domain

### Redirect URLs
Add these redirect URLs:
- `http://localhost:3000/auth/callback` (for development)
- `https://yourdomain.com/auth/callback` (for production)

### Email Templates (Optional)
You can customize the email templates for:
- Confirm signup
- Reset password
- Magic link

## 5. Set Up Database Schema (Optional)

If you want to store additional user profile information, you can create a profiles table:

```sql
-- Create a profiles table
create table profiles (
  id uuid references auth.users on delete cascade not null primary key,
  updated_at timestamp with time zone,
  username text unique,
  full_name text,
  avatar_url text,
  website text,

  constraint username_length check (char_length(username) >= 3)
);

-- Set up Row Level Security (RLS)
alter table profiles enable row level security;

create policy "Public profiles are viewable by everyone." on profiles
  for select using (true);

create policy "Users can insert their own profile." on profiles
  for insert with check (auth.uid() = id);

create policy "Users can update own profile." on profiles
  for update using (auth.uid() = id);

-- Set up automatic profile creation
create function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, full_name, avatar_url)
  values (new.id, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');
  return new;
end;
$$ language plpgsql security definer;

create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();
```

## 6. Test Your Setup

1. Start your development server: `npm run dev`
2. Navigate to the sign-up page
3. Try creating a new account
4. Check your email for the confirmation link
5. Try signing in with your new account

## 7. Production Deployment

When deploying to production:

1. Update your environment variables in your hosting platform (Vercel, Netlify, etc.)
2. Update the Site URL and Redirect URLs in Supabase dashboard
3. Consider enabling additional security features like:
   - Email confirmation required
   - Phone confirmation
   - Multi-factor authentication

## Troubleshooting

### Common Issues:

1. **"Invalid login credentials"**
   - Make sure the user has confirmed their email
   - Check if the email/password combination is correct

2. **"Email not confirmed"**
   - Check the user's email for the confirmation link
   - Resend confirmation email from Supabase dashboard

3. **CORS errors**
   - Make sure your Site URL is correctly configured in Supabase
   - Check that redirect URLs are properly set

4. **Environment variables not loading**
   - Restart your development server after updating .env
   - Make sure .env is in your project root
   - Check that variables start with NEXT_PUBLIC_ for client-side access

## Next Steps

- Customize the authentication UI components
- Add social login providers (Google, GitHub, etc.)
- Implement role-based access control
- Set up user profiles and additional user data
