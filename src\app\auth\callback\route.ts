import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
    const { searchParams, origin } = new URL(request.url)
    const code = searchParams.get('code')
    const next = searchParams.get('next') ?? '/dashboard'

    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL === 'your_supabase_project_url') {
        console.warn('Supabase not configured. Please set NEXT_PUBLIC_SUPABASE_URL in your environment variables.')
        return NextResponse.redirect(`${origin}/auth/auth-code-error`)
    }

    if (code) {
        try {
            // Dynamically import supabase only when needed and configured
            const { supabase } = await import('@/lib/supabase')
            const { error } = await supabase.auth.exchangeCodeForSession(code)
            if (!error) {
                return NextResponse.redirect(`${origin}${next}`)
            }
        } catch (error) {
            console.error('Supabase auth error:', error)
        }
    }

    // Return the user to an error page with instructions
    return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}
