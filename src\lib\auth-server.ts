import { createSupabaseAdmin, isSupabaseConfigured } from './supabase'
import type { SignUpCredential, SignInCredential } from '@/@types/auth'

// Server-side authentication functions
export const serverAuthService = {
  // Create user (admin function)
  async createUser(credentials: SignUpCredential) {
    if (!isSupabaseConfigured()) {
      throw new Error('Authentication is not configured.')
    }

    const supabaseAdmin = createSupabaseAdmin()
    
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email: credentials.email,
      password: credentials.password,
      email_confirm: true // Auto-confirm for admin-created users
    })

    if (error) {
      throw new Error(error.message)
    }

    return {
      user: {
        id: data.user.id,
        email: data.user.email!
      }
    }
  },

  // Validate user credentials (for NextAuth)
  async validateCredentials(credentials: SignInCredential) {
    if (!isSupabaseConfigured()) {
      console.warn('Supabase not configured, skipping validation')
      return null
    }

    try {
      const supabaseAdmin = createSupabaseAdmin()
      
      const { data, error } = await supabaseAdmin.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password
      })

      if (error || !data.user) {
        return null
      }

      return {
        id: data.user.id,
        email: data.user.email!,
        name: data.user.email!.split('@')[0], // Use email prefix as name
        image: data.user.user_metadata?.avatar_url || null
      }
    } catch (error) {
      console.error('Server auth validation error:', error)
      return null
    }
  },

  // Send password reset email
  async sendPasswordReset(email: string) {
    if (!isSupabaseConfigured()) {
      throw new Error('Authentication is not configured.')
    }

    const supabaseAdmin = createSupabaseAdmin()
    
    const { error } = await supabaseAdmin.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/reset-password`
    })

    if (error) {
      throw new Error(error.message)
    }

    return { success: true }
  }
}
