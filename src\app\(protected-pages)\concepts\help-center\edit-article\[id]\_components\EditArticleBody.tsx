'use client'

import Tool<PERSON>uttonBold from '@/components/shared/RichTextEditor/toolButtons/ToolButtonBold'
import Tool<PERSON>uttonItalic from '@/components/shared/RichTextEditor/toolButtons/ToolButtonItalic'
import ToolButtonStrike from '@/components/shared/RichTextEditor/toolButtons/ToolButtonStrike'
import ToolButtonCode from '@/components/shared/RichTextEditor/toolButtons/ToolButtonCode'
import ToolButtonOrderedList from '@/components/shared/RichTextEditor/toolButtons/ToolButtonOrderedList'
import Too<PERSON><PERSON>utton<PERSON>ode<PERSON>lock from '@/components/shared/RichTextEditor/toolButtons/ToolButtonCodeBlock'
import ToolButtonBlockquote from '@/components/shared/RichTextEditor/toolButtons/ToolButtonBlockquote'
import ToolButtonHorizontalRule from '@/components/shared/RichTextEditor/toolButtons/ToolButtonHorizontalRule'
import ToolButtonHeading from '@/components/shared/RichTextEditor/toolButtons/ToolButtonHeading'
import Too<PERSON><PERSON><PERSON>on<PERSON>ulletList from '@/components/shared/RichTextEditor/toolButtons/ToolButtonBulletList'
import Loading from '@/components/shared/Loading'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'

type EditArticleBodyProps = {
    content?: string
}

const EditArticleBody = ({ content }: EditArticleBodyProps) => {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                bulletList: {
                    keepMarks: true,
                },
                orderedList: {
                    keepMarks: true,
                },
            }),
        ],
        editorProps: {
            attributes: {
                class: 'm-2 focus:outline-hidden',
            },
        },
        content,
    })

    if (!editor)
        return (
            <div className="min-h-[calc(100vh-550px)] flex items-center justify-center">
                <Loading loading />
            </div>
        )

    return (
        <div>
            <div className="flex gap-x-1 gap-y-2 p-2 border-t border-b border-gray-200 dark:border-gray-700">
                <ToolButtonBold editor={editor} />
                <ToolButtonItalic editor={editor} />
                <ToolButtonStrike editor={editor} />
                <ToolButtonCode editor={editor} />
                <ToolButtonBlockquote editor={editor} />
                <ToolButtonHeading editor={editor} />
                <ToolButtonBulletList editor={editor} />
                <ToolButtonOrderedList editor={editor} />
                <ToolButtonCodeBlock editor={editor} />
                <ToolButtonHorizontalRule editor={editor} />
            </div>
            <EditorContent
                className="min-h-[200px] overflow-auto px-2 prose max-w-full"
                editor={editor}
            />
        </div>
    )
}

export default EditArticleBody
