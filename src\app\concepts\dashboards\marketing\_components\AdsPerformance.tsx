'use client'

import Card from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'

interface AdsPerformanceProps {
    data: {
        campagin: number[]
        email: number[]
        label: string[]
    }
}

const AdsPerformance = ({ data }: AdsPerformanceProps) => {
    const chartSeries = [
        {
            name: 'Campaign Ads',
            data: data?.campagin || []
        },
        {
            name: 'Email Marketing',
            data: data?.email || []
        }
    ]

    return (
        <Card>
            <h5 className="mb-6">Ads Performance</h5>
            <Chart
                type="line"
                height={350}
                series={chartSeries}
                xAxis={data?.label || []}
                customOptions={{
                    colors: ['#3B82F6', '#10B981'],
                    stroke: {
                        width: 3,
                        curve: 'smooth'
                    },
                    markers: {
                        size: 5,
                        hover: {
                            size: 7
                        }
                    },
                    grid: {
                        borderColor: '#e5e7eb',
                        strokeDashArray: 5
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right'
                    },
                    tooltip: {
                        shared: true,
                        intersect: false
                    }
                }}
            />
        </Card>
    )
}

export default AdsPerformance
