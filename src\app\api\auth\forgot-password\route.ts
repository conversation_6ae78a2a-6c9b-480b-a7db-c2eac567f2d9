import { NextRequest, NextResponse } from 'next/server'
import { serverAuthService } from '@/lib/auth-server'
import type { ForgotPassword } from '@/@types/auth'

export async function POST(request: NextRequest) {
    try {
        const body: ForgotPassword = await request.json()

        if (!body.email) {
            return NextResponse.json(
                { error: 'Email is required' },
                { status: 400 }
            )
        }

        const result = await serverAuthService.sendPasswordReset(body.email)
        return NextResponse.json(result)
    } catch (error) {
        console.error('Forgot password error:', error)
        const errorMessage = error instanceof Error ? error.message : 'An error occurred'
        return NextResponse.json({ error: errorMessage }, { status: 400 })
    }
}
