'use client'

import Card from '@/components/ui/Card'
import Avatar from '@/components/ui/Avatar'
import Tag from '@/components/ui/Tag'
import { TbTicket, TbMessage, TbTag, TbFile, TbUserPlus } from 'react-icons/tb'

interface RecentActivityProps {
    data: Array<{
        type: string
        dateTime: string
        ticket?: string
        status?: number
        userName: string
        userImg?: string
        comment?: string
        tags?: string[]
        files?: string[]
        assignee?: string
    }>
}

const RecentActivity = ({ data }: RecentActivityProps) => {
    const getActivityIcon = (type: string) => {
        switch (type) {
            case 'UPDATE-TICKET':
                return <TbTicket className="w-4 h-4" />
            case 'COMMENT':
            case 'COMMENT-MENTION':
                return <TbMessage className="w-4 h-4" />
            case 'ADD-TAGS-TO-TICKET':
                return <TbTag className="w-4 h-4" />
            case 'ADD-FILES-TO-TICKET':
                return <TbFile className="w-4 h-4" />
            case 'ASSIGN-TICKET':
                return <TbUserPlus className="w-4 h-4" />
            default:
                return <TbTicket className="w-4 h-4" />
        }
    }

    const getActivityColor = (type: string) => {
        switch (type) {
            case 'UPDATE-TICKET':
                return 'bg-blue-100 text-blue-600'
            case 'COMMENT':
            case 'COMMENT-MENTION':
                return 'bg-emerald-100 text-emerald-600'
            case 'ADD-TAGS-TO-TICKET':
                return 'bg-purple-100 text-purple-600'
            case 'ADD-FILES-TO-TICKET':
                return 'bg-amber-100 text-amber-600'
            case 'ASSIGN-TICKET':
                return 'bg-pink-100 text-pink-600'
            default:
                return 'bg-gray-100 text-gray-600'
        }
    }

    const renderActivityContent = (activity: {
        type: string
        userName: string
        ticket?: string
        comment?: string
        tags?: string[]
        files?: string[]
        assignee?: string
    }) => {
        switch (activity.type) {
            case 'UPDATE-TICKET':
                return (
                    <div>
                        <span className="font-medium">{activity.userName}</span> updated ticket{' '}
                        <span className="font-mono text-sm bg-gray-100 px-1 rounded">{activity.ticket}</span>
                    </div>
                )
            case 'COMMENT':
                return (
                    <div>
                        <span className="font-medium">{activity.userName}</span> commented:
                        <div className="text-sm text-gray-600 mt-1 italic">
                            &ldquo;{activity.comment?.substring(0, 100)}...&rdquo;
                        </div>
                    </div>
                )
            case 'COMMENT-MENTION':
                return (
                    <div>
                        <span className="font-medium">{activity.userName}</span> mentioned someone:
                        <div className="text-sm text-gray-600 mt-1 italic">
                            &ldquo;{activity.comment?.substring(0, 100)}...&rdquo;
                        </div>
                    </div>
                )
            case 'ADD-TAGS-TO-TICKET':
                return (
                    <div>
                        <span className="font-medium">{activity.userName}</span> added tags:
                        <div className="flex gap-1 mt-1">
                            {activity.tags?.map((tag: string, index: number) => (
                                <Tag key={index} className="text-xs">
                                    {tag}
                                </Tag>
                            ))}
                        </div>
                    </div>
                )
            case 'ADD-FILES-TO-TICKET':
                return (
                    <div>
                        <span className="font-medium">{activity.userName}</span> added files to{' '}
                        <span className="font-mono text-sm bg-gray-100 px-1 rounded">{activity.ticket}</span>:
                        <div className="text-sm text-gray-600 mt-1">
                            {activity.files?.join(', ')}
                        </div>
                    </div>
                )
            case 'ASSIGN-TICKET':
                return (
                    <div>
                        <span className="font-medium">{activity.userName}</span> assigned{' '}
                        <span className="font-mono text-sm bg-gray-100 px-1 rounded">{activity.ticket}</span> to{' '}
                        <span className="font-medium">{activity.assignee}</span>
                    </div>
                )
            default:
                return (
                    <div>
                        <span className="font-medium">{activity.userName}</span> performed an action
                    </div>
                )
        }
    }

    return (
        <Card>
            <h5 className="mb-6">Recent Activity</h5>
            <div className="space-y-4">
                {data?.slice(0, 6).map((activity, index) => (
                    <div key={index} className="flex items-start gap-3">
                        <div className={`p-2 rounded-lg ${getActivityColor(activity.type)}`}>
                            {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="text-sm">
                                {renderActivityContent(activity)}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                                {activity.dateTime}
                            </div>
                        </div>
                        {activity.userImg && (
                            <Avatar
                                src={activity.userImg}
                                size="sm"
                                className="bg-primary text-white"
                            >
                                {activity.userName.charAt(0)}
                            </Avatar>
                        )}
                    </div>
                ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button className="text-sm text-primary hover:text-primary-dark font-medium">
                    View all activity
                </button>
            </div>
        </Card>
    )
}

export default RecentActivity
