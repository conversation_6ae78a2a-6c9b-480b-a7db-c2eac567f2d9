'use client'

import Card from '@/components/ui/Card'
import DataTable from '@/components/shared/DataTable'
import Tag from '@/components/ui/Tag'
import Avatar from '@/components/ui/Avatar'
import { TbCreditCard } from 'react-icons/tb'
import type { ColumnDef } from '@tanstack/react-table'

interface OrderData {
    id: string
    date: string
    customer: string
    status: number
    paymentMehod: string
    paymentIdendifier: string
    totalAmount: number
}

interface RecentOrderProps {
    data: OrderData[]
}

const RecentOrder = ({ data }: RecentOrderProps) => {
    const getStatusColor = (status: number) => {
        switch (status) {
            case 0:
                return 'bg-amber-100 text-amber-600 border-amber-200'
            case 1:
                return 'bg-emerald-100 text-emerald-600 border-emerald-200'
            case 2:
                return 'bg-red-100 text-red-600 border-red-200'
            default:
                return 'bg-gray-100 text-gray-600 border-gray-200'
        }
    }

    const getStatusText = (status: number) => {
        switch (status) {
            case 0:
                return 'Pending'
            case 1:
                return 'Completed'
            case 2:
                return 'Cancelled'
            default:
                return 'Unknown'
        }
    }

    const getPaymentIcon = (method: string) => {
        const iconClass = "w-4 h-4"
        switch (method.toLowerCase()) {
            case 'visa':
                return <div className={`${iconClass} bg-blue-600 text-white rounded flex items-center justify-center text-xs font-bold`}>V</div>
            case 'master':
                return <div className={`${iconClass} bg-red-600 text-white rounded flex items-center justify-center text-xs font-bold`}>M</div>
            case 'paypal':
                return <div className={`${iconClass} bg-blue-500 text-white rounded flex items-center justify-center text-xs font-bold`}>P</div>
            default:
                return <TbCreditCard className={iconClass} />
        }
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount)
    }

    const columns: ColumnDef<OrderData>[] = [
        {
            header: 'Order ID',
            accessorKey: 'id',
            cell: ({ row }: { row: { original: OrderData } }) => (
                <span className="font-mono text-sm">#{row.original.id}</span>
            )
        },
        {
            header: 'Customer',
            accessorKey: 'customer',
            cell: ({ row }: { row: { original: OrderData } }) => (
                <div className="flex items-center gap-2">
                    <Avatar size="sm" className="bg-primary text-white">
                        {row.original.customer.charAt(0)}
                    </Avatar>
                    <span className="font-medium">{row.original.customer}</span>
                </div>
            )
        },
        {
            header: 'Date',
            accessorKey: 'date',
            cell: ({ row }: { row: { original: OrderData } }) => (
                <span className="text-sm">{row.original.date}</span>
            )
        },
        {
            header: 'Payment',
            accessorKey: 'payment',
            cell: ({ row }: { row: { original: OrderData } }) => (
                <div className="flex items-center gap-2">
                    {getPaymentIcon(row.original.paymentMehod)}
                    <span className="text-sm">{row.original.paymentIdendifier}</span>
                </div>
            )
        },
        {
            header: 'Status',
            accessorKey: 'status',
            cell: ({ row }: { row: { original: OrderData } }) => (
                <Tag className={`${getStatusColor(row.original.status)} border`}>
                    {getStatusText(row.original.status)}
                </Tag>
            )
        },
        {
            header: 'Amount',
            accessorKey: 'totalAmount',
            cell: ({ row }: { row: { original: OrderData } }) => (
                <span className="font-semibold">
                    {formatCurrency(row.original.totalAmount)}
                </span>
            )
        }
    ]

    return (
        <Card>
            <div className="flex items-center justify-between mb-6">
                <h5>Recent Orders</h5>
                <span className="text-sm text-gray-500">
                    {data?.length || 0} orders
                </span>
            </div>
            <DataTable
                data={data || []}
                columns={columns}
                noData={!data || data.length === 0}
            />
        </Card>
    )
}

export default RecentOrder
