'use client'

import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import Progress from '@/components/ui/Progress'
import {
    PiArrowUpRightDuotone,
    PiStackDuotone,
    PiChartLineUpDuotone,
    PiUsersDuotone
} from 'react-icons/pi'

const OverviewTab = () => {
    return (
        <div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Current Plan Summary */}
                <Card className="lg:col-span-2">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h5 className="font-bold text-lg mb-1">Current Plan</h5>
                            <div className="flex items-center">
                                <StatusBadge className="bg-emerald-100 text-emerald-600 border border-emerald-200 mr-2">
                                    Growth
                                </StatusBadge>
                                <span className="text-sm text-gray-500">Renews on Jan 15, 2024</span>
                            </div>
                        </div>
                        <Button
                            size="sm"
                            variant="solid"
                            className="mt-2 md:mt-0 bg-emerald-500 hover:bg-emerald-600"
                            icon={<PiArrowUpRightDuotone />}
                        >
                            Upgrade Plan
                        </Button>
                    </div>

                    <div className="mb-4">
                        <div className="flex justify-between text-sm mb-1">
                            <span>Plan Usage</span>
                            <span>1 of 5 frameworks activated</span>
                        </div>
                        <Progress percent={20} customColorClass="bg-emerald-500" />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                                <span className="text-emerald-500 mr-2"><PiStackDuotone className="text-xl" /></span>
                                <span className="font-medium">Frameworks</span>
                            </div>
                            <div className="text-2xl font-bold">1/5</div>
                            <div className="text-xs text-gray-500">Active frameworks</div>
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                                <span className="text-blue-500 mr-2"><PiUsersDuotone className="text-xl" /></span>
                                <span className="font-medium">Team Members</span>
                            </div>
                            <div className="text-2xl font-bold">3</div>
                            <div className="text-xs text-gray-500">Active users</div>
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                                <span className="text-purple-500 mr-2"><PiChartLineUpDuotone className="text-xl" /></span>
                                <span className="font-medium">Compliance Score</span>
                            </div>
                            <div className="text-2xl font-bold">62%</div>
                            <div className="text-xs text-gray-500">Overall progress</div>
                        </div>
                    </div>
                </Card>

                {/* Account Summary */}
                <Card>
                    <h5 className="font-bold text-lg mb-4">Account Summary</h5>
                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Organization</span>
                            <span className="text-sm font-medium">CheckGap Demo</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Plan</span>
                            <span className="text-sm font-medium">Growth</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Next Billing</span>
                            <span className="text-sm font-medium">Jan 15, 2024</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Storage Used</span>
                            <span className="text-sm font-medium">2.1 GB / 10 GB</span>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Recent Activity */}
            <Card className="mt-6">
                <h5 className="font-bold text-lg mb-4">Recent Activity</h5>
                <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                            <span className="text-sm">Account settings updated</span>
                        </div>
                        <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                            <span className="text-sm">PCI DSS framework activated</span>
                        </div>
                        <span className="text-xs text-gray-500">1 day ago</span>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                            <span className="text-sm">Team member invited</span>
                        </div>
                        <span className="text-xs text-gray-500">3 days ago</span>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default OverviewTab
