const FileFigma = ({
    height = 100,
    width = 100,
}: {
    height?: number
    width?: number
}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 287.82 384"
            width={width}
            height={height}
        >
            <path
                fill="#2b2b32"
                d="M652.52 792.45h192s50.06.1 49-59.26 0-210.58 0-210.58H785.16l-1-114.16H652.52s-45.94 1.52-46.52 53 0 284.71 0 284.71 4.19 44.97 46.52 46.29z"
                transform="translate(-605.74 -408.45)"
            ></path>
            <path
                fill="#4c4c54"
                d="M178.39 0L287.82 114.16 179.42 114.16 178.39 0z"
            ></path>
            <path
                fill="#1abcfe"
                fillRule="evenodd"
                d="M749.08 649.62a32.53 32.53 0 1132.53 32.53 32.52 32.52 0 01-32.53-32.53z"
                transform="translate(-605.74 -408.45)"
            ></path>
            <path
                fill="#0acf83"
                fillRule="evenodd"
                d="M684 714.68a32.52 32.52 0 0132.53-32.53h32.53v32.53a32.53 32.53 0 01-65.06 0z"
                transform="translate(-605.74 -408.45)"
            ></path>
            <path
                fill="#ff7262"
                fillRule="evenodd"
                d="M749.08 552v65.06h32.53a32.53 32.53 0 000-65.06z"
                transform="translate(-605.74 -408.45)"
            ></path>
            <path
                fill="#f24e1e"
                fillRule="evenodd"
                d="M684 584.56a32.52 32.52 0 0032.53 32.53h32.53V552h-32.51A32.52 32.52 0 00684 584.56z"
                transform="translate(-605.74 -408.45)"
            ></path>
            <path
                fill="#a259ff"
                fillRule="evenodd"
                d="M684 649.62a32.52 32.52 0 0032.53 32.53h32.53v-65.06h-32.51A32.52 32.52 0 00684 649.62z"
                transform="translate(-605.74 -408.45)"
            ></path>
        </svg>
    )
}

export default FileFigma
