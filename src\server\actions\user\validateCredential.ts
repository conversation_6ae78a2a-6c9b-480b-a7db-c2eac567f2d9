'use server'

import type { SignInCredential } from '@/@types/auth'

const validateCredential = async (values: SignInCredential) => {
  try {
    // Dynamically import to avoid middleware issues
    const { serverAuthService } = await import('@/lib/auth-server')
    
    console.log('Validating credentials for:', values.email)
    
    const user = await serverAuthService.validateCredentials(values)
    
    if (user) {
      console.log('Authentication successful for:', user.email)
    } else {
      console.log('Authentication failed for:', values.email)
    }
    
    return user
  } catch (error) {
    console.error('Credential validation error:', error)
    return null
  }
}

export default validateCredential
