import Link from 'next/link'

export default function AuthCodeError() {
    return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">
                        Authentication Error
                    </h1>
                    <p className="text-gray-600 mb-6">
                        Sorry, we couldn&apos;t authenticate you. This could be due to an expired or invalid link.
                    </p>
                    <div className="space-y-3">
                        <Link
                            href="/sign-in"
                            className="block w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Try signing in again
                        </Link>
                        <Link
                            href="/sign-up"
                            className="block w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
                        >
                            Create a new account
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    )
}
