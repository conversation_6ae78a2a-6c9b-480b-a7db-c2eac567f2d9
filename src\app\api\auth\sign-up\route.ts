import { NextRequest, NextResponse } from 'next/server'
import { serverAuthService } from '@/lib/auth-server'
import type { SignUpCredential } from '@/@types/auth'

export async function POST(request: NextRequest) {
    try {
        const body: SignUpCredential = await request.json()

        // Validate input
        if (!body.email || !body.password) {
            return NextResponse.json(
                { error: 'Email and password are required' },
                { status: 400 }
            )
        }

        const result = await serverAuthService.createUser(body)
        return NextResponse.json(result)
    } catch (error) {
        console.error('Sign up error:', error)
        const errorMessage = error instanceof Error ? error.message : 'An error occurred during sign up'
        return NextResponse.json({ error: errorMessage }, { status: 400 })
    }
}
