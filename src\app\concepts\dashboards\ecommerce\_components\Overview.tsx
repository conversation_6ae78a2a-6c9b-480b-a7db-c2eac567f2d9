'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'
import Segment from '@/components/ui/Segment'
import { TbTrendingUp, TbTrendingDown } from 'react-icons/tb'

interface OverviewProps {
    data: {
        totalProfit: {
            [key: string]: {
                value: number
                growShrink: number
                comparePeriod: string
                chartData: {
                    series: Array<{ name: string; data: number[] }>
                    date: string[]
                }
            }
        }
        totalOrder: {
            [key: string]: {
                value: number
                growShrink: number
                comparePeriod: string
                chartData: {
                    series: Array<{ name: string; data: number[] }>
                    date: string[]
                }
            }
        }
        totalImpression: {
            [key: string]: {
                value: number
                growShrink: number
                comparePeriod: string
                chartData: {
                    series: Array<{ name: string; data: number[] }>
                    date: string[]
                }
            }
        }
    }
}

const Overview = ({ data }: OverviewProps) => {
    const [selectedPeriod, setSelectedPeriod] = useState('thisWeek')

    const periodOptions = [
        { label: 'This Week', value: 'thisWeek' },
        { label: 'This Month', value: 'thisMonth' },
        { label: 'This Year', value: 'thisYear' }
    ]

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value)
    }

    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('en-US').format(value)
    }

    const renderGrowthIndicator = (growShrink: number) => {
        const isPositive = growShrink > 0
        return (
            <div className={`flex items-center gap-1 ${isPositive ? 'text-emerald-500' : 'text-red-500'}`}>
                {isPositive ? <TbTrendingUp /> : <TbTrendingDown />}
                <span className="text-sm font-medium">{Math.abs(growShrink)}%</span>
            </div>
        )
    }

    const currentData = {
        profit: data.totalProfit?.[selectedPeriod],
        orders: data.totalOrder?.[selectedPeriod],
        impressions: data.totalImpression?.[selectedPeriod]
    }

    return (
        <Card>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                <h4>Overview</h4>
                <Segment
                    value={selectedPeriod}
                    onChange={(value) => setSelectedPeriod(value as string)}
                    size="sm"
                >
                    {periodOptions.map(option => (
                        <Segment.Item key={option.value} value={option.value}>
                            {option.label}
                        </Segment.Item>
                    ))}
                </Segment>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Total Profit */}
                <div className="space-y-4">
                    <div>
                        <div className="text-2xl font-bold">
                            {formatCurrency(currentData.profit?.value || 0)}
                        </div>
                        <div className="text-sm text-gray-500">Total Profit</div>
                        <div className="flex items-center justify-between mt-2">
                            {renderGrowthIndicator(currentData.profit?.growShrink || 0)}
                            <span className="text-xs text-gray-400">
                                {currentData.profit?.comparePeriod}
                            </span>
                        </div>
                    </div>
                    <Chart
                        type="area"
                        height={120}
                        series={currentData.profit?.chartData?.series || []}
                        xAxis={currentData.profit?.chartData?.date || []}
                        customOptions={{
                            chart: { sparkline: { enabled: true } },
                            colors: ['#3B82F6']
                        }}
                    />
                </div>

                {/* Total Orders */}
                <div className="space-y-4">
                    <div>
                        <div className="text-2xl font-bold">
                            {formatNumber(currentData.orders?.value || 0)}
                        </div>
                        <div className="text-sm text-gray-500">Total Orders</div>
                        <div className="flex items-center justify-between mt-2">
                            {renderGrowthIndicator(currentData.orders?.growShrink || 0)}
                            <span className="text-xs text-gray-400">
                                {currentData.orders?.comparePeriod}
                            </span>
                        </div>
                    </div>
                    <Chart
                        type="area"
                        height={120}
                        series={currentData.orders?.chartData?.series || []}
                        xAxis={currentData.orders?.chartData?.date || []}
                        customOptions={{
                            chart: { sparkline: { enabled: true } },
                            colors: ['#10B981']
                        }}
                    />
                </div>

                {/* Total Impressions */}
                <div className="space-y-4">
                    <div>
                        <div className="text-2xl font-bold">
                            {formatNumber(currentData.impressions?.value || 0)}
                        </div>
                        <div className="text-sm text-gray-500">Total Impressions</div>
                        <div className="flex items-center justify-between mt-2">
                            {renderGrowthIndicator(currentData.impressions?.growShrink || 0)}
                            <span className="text-xs text-gray-400">
                                {currentData.impressions?.comparePeriod}
                            </span>
                        </div>
                    </div>
                    <Chart
                        type="area"
                        height={120}
                        series={currentData.impressions?.chartData?.series || []}
                        xAxis={currentData.impressions?.chartData?.date || []}
                        customOptions={{
                            chart: { sparkline: { enabled: true } },
                            colors: ['#F59E0B']
                        }}
                    />
                </div>
            </div>
        </Card>
    )
}

export default Overview
