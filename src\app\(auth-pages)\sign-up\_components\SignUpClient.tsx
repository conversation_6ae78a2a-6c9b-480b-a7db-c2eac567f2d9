'use client'

import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import SignUp from '@/components/auth/SignUp'
import { authService } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import type { OnSignUpPayload } from '@/components/auth/SignUp'

const SignUpClient = () => {
    const router = useRouter()

    const handlSignUp = async ({
        values,
        setSubmitting,
        setMessage,
    }: OnSignUpPayload) => {
        try {
            setSubmitting(true)

            // Convert form values to match API
            const credentials = {
                email: values.email,
                password: values.password
            }

            const result = await authService.signUp(credentials)

            toast.push(
                <Notification title="Success!" type="success">
                    {result.message}
                </Notification>,
            )

            // If email is confirmed, redirect to sign in
            if (result.user?.email_confirmed_at) {
                router.push('/sign-in')
            } else {
                // Stay on page and show message about email verification
                setMessage('Please check your email and click the verification link, then try signing in.')
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'An error occurred during sign up'
            setMessage(errorMessage)
        } finally {
            setSubmitting(false)
        }
    }

    return <SignUp onSignUp={handlSignUp} />
}

export default SignUpClient
