'use client'

import Card from '@/components/ui/Card'
import { Tb<PERSON>older<PERSON><PERSON>, TbFolder<PERSON>heck, TbCalendarTime } from 'react-icons/tb'

interface ProjectOverviewProps {
    data: {
        ongoingProject: number
        projectCompleted: number
        upcomingProject: number
    }
}

const ProjectOverview = ({ data }: ProjectOverviewProps) => {
    const projects = [
        {
            title: 'Ongoing Projects',
            value: data?.ongoingProject || 0,
            icon: <TbFolderOpen className="w-6 h-6" />,
            color: 'bg-blue-500',
            textColor: 'text-blue-600'
        },
        {
            title: 'Completed Projects',
            value: data?.projectCompleted || 0,
            icon: <TbFolderCheck className="w-6 h-6" />,
            color: 'bg-emerald-500',
            textColor: 'text-emerald-600'
        },
        {
            title: 'Upcoming Projects',
            value: data?.upcomingProject || 0,
            icon: <TbCalendarTime className="w-6 h-6" />,
            color: 'bg-amber-500',
            textColor: 'text-amber-600'
        }
    ]

    const totalProjects = (data?.ongoingProject || 0) + (data?.projectCompleted || 0) + (data?.upcomingProject || 0)

    return (
        <Card>
            <h5 className="mb-6">Project Overview</h5>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {projects.map((project, index) => (
                    <div key={index} className="text-center">
                        <div className={`inline-flex p-3 rounded-lg ${project.color} text-white mb-3`}>
                            {project.icon}
                        </div>
                        <div className={`text-2xl font-bold ${project.textColor} mb-1`}>
                            {project.value}
                        </div>
                        <div className="text-sm text-gray-500">
                            {project.title}
                        </div>
                    </div>
                ))}
            </div>

            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Total Projects</span>
                    <span className="font-semibold text-lg">{totalProjects}</span>
                </div>
            </div>
        </Card>
    )
}

export default ProjectOverview
