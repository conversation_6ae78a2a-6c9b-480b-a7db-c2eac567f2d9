'use client'

import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'

interface CustomerDemographicProps {
    data: Array<{
        id: string
        name: string
        value: number
        coordinates: [number, number]
    }>
}

const CustomerDemographic = ({ data }: CustomerDemographicProps) => {
    const getCountryFlag = (countryId: string) => {
        const flags: Record<string, string> = {
            us: '🇺🇸',
            br: '🇧🇷',
            in: '🇮🇳',
            uk: '🇬🇧',
            tr: '🇹🇷'
        }
        return flags[countryId] || '🌍'
    }

    const getProgressColor = (index: number) => {
        const colors = [
            'bg-blue-500',
            'bg-emerald-500',
            'bg-amber-500',
            'bg-purple-500',
            'bg-pink-500'
        ]
        return colors[index % colors.length]
    }

    const sortedData = data?.sort((a, b) => b.value - a.value) || []

    return (
        <Card>
            <h5 className="mb-6">Customer Demographics</h5>
            <div className="space-y-4">
                {sortedData.map((country, index) => (
                    <div key={country.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <span className="text-xl">{getCountryFlag(country.id)}</span>
                                <span className="font-medium">{country.name}</span>
                            </div>
                            <span className="font-semibold text-sm">
                                {country.value.toFixed(1)}%
                            </span>
                        </div>
                        <Progress
                            percent={country.value}
                            customColorClass={getProgressColor(index)}
                            showInfo={false}
                            size="sm"
                        />
                    </div>
                ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-500 text-center">
                    Based on customer location data
                </div>
            </div>
        </Card>
    )
}

export default CustomerDemographic
