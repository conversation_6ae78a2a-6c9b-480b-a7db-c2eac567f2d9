export const DIR_RTL = 'rtl'
export const DIR_LTR = 'ltr'
export const MODE_LIGHT = 'light'
export const MODE_DARK = 'dark'

export const SIDE_NAV_WIDTH = 290
export const SIDE_NAV_COLLAPSED_WIDTH = 80
export const SPLITTED_SIDE_NAV_MINI_WIDTH = 80
export const STACKED_SIDE_NAV_SECONDARY_WIDTH = 270
export const SIDE_NAV_CONTENT_GUTTER = 'px-2'
export const LOGO_X_GUTTER = 'px-6'
export const HEADER_HEIGHT = 64
export const PAGE_CONTAINER_GUTTER_X = 'px-4 sm:px-6 md:px-8'
export const PAGE_CONTAINER_GUTTER_Y = 'py-4 sm:py-6 md:px-8'

export const LAYOUT_COLLAPSIBLE_SIDE = 'collapsibleSide'
export const LAYOUT_STACKED_SIDE = 'stackedSide'
export const LAYOUT_TOP_BAR_CLASSIC = 'topBarClassic'
export const LAYOUT_FRAMELESS_SIDE = 'framelessSide'
export const LAYOUT_CONTENT_OVERLAY = 'contentOverlay'
export const LAYOUT_BLANK = 'blank'

export const THEME_ENUM = {
    DIR_RTL: DIR_RTL,
    DIR_LTR: DIR_LTR,
    MODE_LIGHT: MODE_LIGHT,
    MODE_DARK: MODE_DARK,
    SIDE_NAV_WIDTH: SIDE_NAV_WIDTH,
    SIDE_NAV_COLLAPSED_WIDTH: SIDE_NAV_COLLAPSED_WIDTH,
    SPLITTED_SIDE_NAV_MINI_WIDTH: SPLITTED_SIDE_NAV_MINI_WIDTH,
    STACKED_SIDE_NAV_SECONDARY_WIDTH: STACKED_SIDE_NAV_SECONDARY_WIDTH,
    HEADER_HEIGHT: HEADER_HEIGHT,
    LAYOUT_COLLAPSIBLE_SIDE: LAYOUT_COLLAPSIBLE_SIDE,
    LAYOUT_STACKED_SIDE: LAYOUT_STACKED_SIDE,
    LAYOUT_TOP_BAR_CLASSIC: LAYOUT_TOP_BAR_CLASSIC,
    LAYOUT_FRAMELESS_SIDE: LAYOUT_FRAMELESS_SIDE,
    LAYOUT_CONTENT_OVERLAY: LAYOUT_CONTENT_OVERLAY,
} as const
