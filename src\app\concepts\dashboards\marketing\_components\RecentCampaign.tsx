'use client'

import Card from '@/components/ui/Card'
import DataTable from '@/components/shared/DataTable'
import Tag from '@/components/ui/Tag'
import Progress from '@/components/ui/Progress'
import type { ColumnDef } from '@tanstack/react-table'

interface CampaignData {
    id: string
    name: string
    startDate: number
    endDate: number
    budget: number
    leadsGenerated: number
    conversions: number
    conversionRate: number
    status: string
    type: string
    audienceGroup: string[]
}

interface RecentCampaignProps {
    data: CampaignData[]
}

const RecentCampaign = ({ data }: RecentCampaignProps) => {
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'active':
                return 'bg-emerald-100 text-emerald-600 border-emerald-200'
            case 'completed':
                return 'bg-blue-100 text-blue-600 border-blue-200'
            case 'scheduled':
                return 'bg-amber-100 text-amber-600 border-amber-200'
            case 'paused':
                return 'bg-gray-100 text-gray-600 border-gray-200'
            default:
                return 'bg-gray-100 text-gray-600 border-gray-200'
        }
    }

    const getTypeColor = (type: string) => {
        switch (type.toLowerCase()) {
            case 'promotional':
                return 'bg-purple-100 text-purple-600 border-purple-200'
            case 'seasonal':
                return 'bg-orange-100 text-orange-600 border-orange-200'
            case 'launch':
                return 'bg-pink-100 text-pink-600 border-pink-200'
            default:
                return 'bg-gray-100 text-gray-600 border-gray-200'
        }
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount)
    }

    const formatDate = (timestamp: number) => {
        return new Date(timestamp).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        })
    }

    const columns: ColumnDef<CampaignData>[] = [
        {
            header: 'Campaign',
            accessorKey: 'name',
            cell: ({ row }: { row: { original: CampaignData } }) => (
                <div>
                    <div className="font-medium">{row.original.name}</div>
                    <div className="text-sm text-gray-500">
                        {row.original.audienceGroup.join(', ')}
                    </div>
                </div>
            )
        },
        {
            header: 'Type',
            accessorKey: 'type',
            cell: ({ row }: { row: { original: CampaignData } }) => (
                <Tag className={`${getTypeColor(row.original.type)} border capitalize`}>
                    {row.original.type}
                </Tag>
            )
        },
        {
            header: 'Duration',
            accessorKey: 'duration',
            cell: ({ row }: { row: { original: CampaignData } }) => (
                <div className="text-sm">
                    {formatDate(row.original.startDate)} - {formatDate(row.original.endDate)}
                </div>
            )
        },
        {
            header: 'Budget',
            accessorKey: 'budget',
            cell: ({ row }: { row: { original: CampaignData } }) => (
                <span className="font-medium">
                    {formatCurrency(row.original.budget)}
                </span>
            )
        },
        {
            header: 'Leads',
            accessorKey: 'leadsGenerated',
            cell: ({ row }: { row: { original: CampaignData } }) => (
                <span className="font-medium">
                    {row.original.leadsGenerated.toLocaleString()}
                </span>
            )
        },
        {
            header: 'Conversion Rate',
            accessorKey: 'conversionRate',
            cell: ({ row }: { row: { original: CampaignData } }) => (
                <div className="space-y-1">
                    <div className="text-sm font-medium">
                        {row.original.conversionRate}%
                    </div>
                    <Progress
                        percent={row.original.conversionRate}
                        showInfo={false}
                        size="sm"
                        customColorClass={
                            row.original.conversionRate >= 8 ? 'bg-emerald-500' :
                            row.original.conversionRate >= 5 ? 'bg-amber-500' : 'bg-red-500'
                        }
                    />
                </div>
            )
        },
        {
            header: 'Status',
            accessorKey: 'status',
            cell: ({ row }: { row: { original: CampaignData } }) => (
                <Tag className={`${getStatusColor(row.original.status)} border capitalize`}>
                    {row.original.status}
                </Tag>
            )
        }
    ]

    return (
        <Card>
            <div className="flex items-center justify-between mb-6">
                <h5>Recent Campaigns</h5>
                <span className="text-sm text-gray-500">
                    {data?.length || 0} campaigns
                </span>
            </div>
            <DataTable
                data={data || []}
                columns={columns}
                noData={!data || data.length === 0}
            />
        </Card>
    )
}

export default RecentCampaign
