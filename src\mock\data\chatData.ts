export const chatList = [
    {
        id: '1',
        name: '<PERSON>',
        userId: '4',
        avatar: '/img/avatars/thumb-4.jpg',
        unread: 3,
        time: 1738384790,
        lastConversation: 'Will do. Appreciate it!',
        chatType: 'personal',
    },
    {
        id: '2',
        name: '<PERSON>',
        userId: '8',
        avatar: '/img/avatars/thumb-8.jpg',
        unread: 3,
        time: 1738334390,
        lastConversation: `Perfect. I'll pack some snacks and drinks.`,
        chatType: 'personal',
    },
    {
        id: '3',
        name: '<PERSON><PERSON>',
        userId: '6',
        avatar: '/img/avatars/thumb-6.jpg',
        unread: 0,
        time: 1738327790,
        lastConversation: 'Okay, Thanks 🥰',
        chatType: 'personal',
    },
    {
        id: '4',
        name: '<PERSON>',
        userId: '3',
        avatar: '/img/avatars/thumb-3.jpg',
        unread: 0,
        time: 1738321190,
        lastConversation: `I'd love that! Let's plan something soon.`,
        chatType: 'personal',
    },
    {
        id: '5',
        name: '<PERSON>',
        userId: '2',
        avatar: '/img/avatars/thumb-2.jpg',
        unread: 0,
        time: 1738367990,
        lastConversation: '😀 No problem. Good luck with your call!',
        chatType: 'personal',
    },
    {
        id: '6',
        name: 'Camila Simmmons',
        userId: '9',
        avatar: '/img/avatars/thumb-9.jpg',
        unread: 0,
        time: 1738351190,
        lastConversation: `True! I'll be more careful around pigeons from now on.`,
        chatType: 'personal',
    },
    {
        id: '7',
        name: 'Team Presentation Prep',
        groupId: '16',
        avatar: '/img/others/img-19.jpg',
        unread: 0,
        time: 1738351190,
        lastConversation: `True! I'll be more careful around pigeons from now on.`,
        chatType: 'groups',
    },
    {
        id: '8',
        name: 'Lunch Squad',
        groupId: '17',
        avatar: '/img/others/img-18.jpg',
        unread: 0,
        time: 1738351190,
        lastConversation: `True! I'll be more careful around pigeons from now on.`,
        chatType: 'groups',
    },
    {
        id: '9',
        name: 'Camping Crew',
        groupId: '18',
        avatar: '/img/others/img-17.jpg',
        unread: 0,
        time: 1738351190,
        lastConversation: `True! I'll be more careful around pigeons from now on.`,
        chatType: 'groups',
    },
]

export const conversationList = [
    {
        id: '1',
        conversation: [
            {
                id: 'b0474144-2815-44c0-bbaf-165edffce238',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Hey, can I ask you something?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'efc25c82-d429-4816-ac46-e6b8760bdbad',
                sender: {
                    id: '4',
                    name: 'Shannon Baker',
                    avatarImageUrl: '/img/avatars/thumb-4.jpg',
                },
                content: `Sure, what's up?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '62d2123f-5c0e-42e8-97ca-2081d760a8c9',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `I'm thinking about applying for a new job, but I'm not sure if it's the right move.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '1351f2fb-0a4e-498c-b165-8e82852b4585',
                sender: {
                    id: '4',
                    name: 'Shannon Baker',
                    avatarImageUrl: '/img/avatars/thumb-4.jpg',
                },
                content: `What's making you hesitate?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '3a6e019c-ce37-4519-92e9-b5c67838c7ca',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `It's a bigger company and a more challenging role. I'm worried it might be too much to handle.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'b8d49337-1c1b-4d3d-ba8f-f0d6ea680e04',
                sender: {
                    id: '4',
                    name: 'Shannon Baker',
                    avatarImageUrl: '/img/avatars/thumb-4.jpg',
                },
                content: `I think you should go for it. You're more than capable and it sounds like a great opportunity for growth.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '7132d40a-cfa0-40c9-97e0-df70f0661079',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Thanks, Mark. I needed that encouragement. I'll start working on my application tonight.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'f9c1e730-7e69-461c-99b4-66fb576e98ce',
                sender: {
                    id: '4',
                    name: 'Shannon Baker',
                    avatarImageUrl: '/img/avatars/thumb-4.jpg',
                },
                content: `Anytime! Let me know if you need any help with your resume or cover letter.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'd92c5df3-d839-47e9-b678-56a20e8db048',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: 'Will do. Appreciate it!',
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
        ],
    },
    {
        id: '2',
        conversation: [
            {
                id: 'aa1db3ab-571a-4180-a2c0-6f34b3d1091d',
                sender: {
                    id: '8',
                    name: 'Jessica Wells',
                    avatarImageUrl: '/img/avatars/thumb-8.jpg',
                },
                content: `Hey, are you free this weekend?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '20b78e0a-b85c-4a45-9fe4-54a988a0096f',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Yeah, I think so. Why, what's up?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'c31b0388-a3dc-42f1-a164-0b0aac702748',
                sender: {
                    id: '8',
                    name: 'Jessica Wells',
                    avatarImageUrl: '/img/avatars/thumb-8.jpg',
                },
                content: `I was thinking of going hiking. The weather's supposed to be great. Interested?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '83efeffe-89db-4793-a199-d5f9052cae09',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Sounds fun! Where are you planning to go?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '58bd1048-f4f5-4413-b507-500fd1ff12ff',
                sender: {
                    id: '8',
                    name: 'Jessica Wells',
                    avatarImageUrl: '/img/avatars/thumb-8.jpg',
                },
                content: `I was thinking of heading to Blue Mountain. It's got some nice trails.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '23a83026-582f-4444-b12e-8129864dd42b',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Cool, count me in! What time were you thinking of leaving?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '0d560a8d-c527-4ed7-9f73-956cd6285e57',
                sender: {
                    id: '8',
                    name: 'Jessica Wells',
                    avatarImageUrl: '/img/avatars/thumb-8.jpg',
                },
                content: `How about 8 AM? That way we can avoid the midday heat.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'd6efc887-6230-40fc-95e3-d3432bc4c67f',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Perfect. I'll pack some snacks and drinks.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
        ],
    },
    {
        id: '3',
        conversation: [
            {
                id: '7a88d884-0b1a-46f6-b218-4ed3870b306e',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Hey, did you get a chance to review the client feedback?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '1df1b437-09f2-41ab-b021-c5d5602a8989',
                sender: {
                    id: '6',
                    name: 'Arlene Pierce',
                    avatarImageUrl: '/img/avatars/thumb-6.jpg',
                },
                content: `Yeah, I looked through it this morning. We need to make a few adjustments to the design.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '937bfa1e-48ca-41ad-a152-1b6ebaca998b',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Agreed. They weren't too happy with the color scheme. Do you have any suggestions?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'a471126c-f382-4059-a73a-a235478308f8',
                sender: {
                    id: '6',
                    name: 'Arlene Pierce',
                    avatarImageUrl: '/img/avatars/thumb-6.jpg',
                },
                content: `Maybe we could try a more neutral palette. Something less bold.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '30d878a1-7736-4be9-ade3-797efef44872',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `That makes sense. I'll put together a few options and send them over for review`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '79200616-fa98-48b6-af21-12b97dd3da82',
                sender: {
                    id: '6',
                    name: 'Arlene Pierce',
                    avatarImageUrl: '/img/avatars/thumb-6.jpg',
                },
                content: `Sounds good. Let's aim to finalize it by tomorrow afternoon.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '32a175f1-dbc5-45cd-b970-4d030ff9e85c',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Okay, Thanks 🥰`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
        ],
    },
    {
        id: '4',
        conversation: [
            {
                id: '5aab30c3-7f59-4e25-b253-e3a8c314bb1b',
                sender: {
                    id: '3',
                    name: 'Max Alexander',
                    avatarImageUrl: '/img/avatars/thumb-3.jpg',
                },
                content: `Hey, long time no see! How have you been?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'c53af73b-cc86-4cba-9ccc-ef124cadc11c',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Hi! I've been good, just busy with work. How about you?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '21413094-37df-4fa4-afd0-9a4dd11ba4d1',
                sender: {
                    id: '3',
                    name: 'Max Alexander',
                    avatarImageUrl: '/img/avatars/thumb-3.jpg',
                },
                content: `Same here. Lots of projects lately. Have you had any time for yourself?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '7dedee29-3eee-4f20-a5c4-59869de632ee',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Not really, but I did manage to catch a new movie last weekend. It was a nice break.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '861e5a3d-729f-458b-bd2d-11beef2841d7',
                sender: {
                    id: '3',
                    name: 'Max Alexander',
                    avatarImageUrl: '/img/avatars/thumb-3.jpg',
                },
                content: `Nice! Which one did you watch?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '9873cc1d-cdcb-4175-8761-4dc0fd8ad97e',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `"The Quantum Realm". It was amazing! You should check it out if you get a chance.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '6468f5b3-1ea0-4f41-8f4b-350443e49b33',
                sender: {
                    id: '3',
                    name: 'Max Alexander',
                    avatarImageUrl: '/img/avatars/thumb-3.jpg',
                },
                content: `I'll add it to my list. Maybe we can go see a movie together sometime.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'd61eaa48-d232-4b05-b3c2-ab5b09c9409d',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `I'd love that! Let's plan something soon.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
        ],
    },
    {
        id: '5',
        conversation: [
            {
                id: 'c37cb214-c523-4c19-a9c3-0900f51528a5',
                sender: {
                    id: '2',
                    name: 'Jeremiah Minsk',
                    avatarImageUrl: '/img/avatars/thumb-2.jpg',
                },
                content: `Hey, are you having issues with the network?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '13cbc996-e7fe-45f4-b731-59fa6477935c',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Yeah, it's been spotty all morning. Super annoying.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '15b4d043-170f-4370-a6ca-e93a8e8c0fb9',
                sender: {
                    id: '2',
                    name: 'Jeremiah Minsk',
                    avatarImageUrl: '/img/avatars/thumb-2.jpg',
                },
                content: `Ugh, same here. I have a call in 10 minutes and I can't get a stable connection.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '0f755039-7c2e-48f8-9e12-f97f5f5b6ae9',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Have you tried restarting your router?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '53434b34-2c92-4a6a-9f0f-843b9fab44bb',
                sender: {
                    id: '2',
                    name: 'Jeremiah Minsk',
                    avatarImageUrl: '/img/avatars/thumb-2.jpg',
                },
                content: `I did, but it didn't help. Might be an ISP issue.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '263d4da0-3cf9-4f51-8879-264aac1d3268',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Bummer. Hope they fix it soon. In the meantime, maybe try using your phone's hotspot?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '614313b5-0c2a-4c9d-8b88-c518ee4ceb1e',
                sender: {
                    id: '2',
                    name: 'Jeremiah Minsk',
                    avatarImageUrl: '/img/avatars/thumb-2.jpg',
                },
                content: `Good idea. I'll give that a shot. Thanks!`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'bfb6f57a-f9de-4faf-a8ac-581e161dd773',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `😀 No problem. Good luck with your call!`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
        ],
    },
    {
        id: '6',
        conversation: [
            {
                id: '3c52dfaf-cdc2-4686-be55-f027f638d044',
                sender: {
                    id: '9',
                    name: 'Camila Simmmons',
                    avatarImageUrl: '/img/avatars/thumb-9.jpg',
                },
                content: `You won't believe what happened to me today.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '6be52021-bd5c-4bfb-8aa1-581728536c48',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `What? Tell me!`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '9d704e57-92ab-49f5-a844-2187b47a6999',
                sender: {
                    id: '9',
                    name: 'Camila Simmmons',
                    avatarImageUrl: '/img/avatars/thumb-9.jpg',
                },
                content: `I was walking to work and a pigeon flew right into me!`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '4258987a-1933-4663-96e9-490142b1df63',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `No way! Did it hurt?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '23099425-dcc6-4fdf-9bfe-82118eef6d92',
                sender: {
                    id: '9',
                    name: 'Camila Simmmons',
                    avatarImageUrl: '/img/avatars/thumb-9.jpg',
                },
                content: `Not really, but it did leave a mess on my shirt. I must have looked ridiculous.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '25e531f8-3c8e-4666-82cd-7034ef2f192c',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Eww, that's hilarious! Did anyone see?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'fdae9ad0-5ecc-447d-a2b0-a81c126e8564',
                sender: {
                    id: '9',
                    name: 'Camila Simmmons',
                    avatarImageUrl: '/img/avatars/thumb-9.jpg',
                },
                content: `Unfortunately, yes. A whole group of tourists. They were laughing too.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '9455ba94-b1b2-41be-89c2-b9c847859535',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Well, at least you gave them a good story to tell!`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '9ae60503-9982-42b7-9173-f193d851c8d0',
                sender: {
                    id: '9',
                    name: 'Camila Simmmons',
                    avatarImageUrl: '/img/avatars/thumb-9.jpg',
                },
                content: `True! I'll be more careful around pigeons from now on.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
        ],
    },
    {
        id: '7',
        conversation: [
            {
                id: '1f3e9a66-8df4-43b9-a3ae-bb6e8de55f9f',
                sender: {
                    id: '10',
                    name: 'Earl Miles',
                    avatarImageUrl: '/img/avatars/thumb-10.jpg',
                },
                content: `Hey team, are we all set for the presentation tomorrow?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '75bafae1-b1a3-4de7-8803-c6c6b7998b8b',
                sender: {
                    id: '11',
                    name: 'Steve Sutton',
                    avatarImageUrl: '/img/avatars/thumb-11.jpg',
                },
                content: `I'm still tweaking the slides a bit. Should be done in an hour.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '9f5066d3-648a-4e77-a8da-d520ebc8974e',
                sender: {
                    id: '12',
                    name: 'Miriam Herrera',
                    avatarImageUrl: '/img/avatars/thumb-12.jpg',
                },
                content: `I finished the financial analysis section. Should I email it to everyone?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '4db7dbea-528c-4f95-bb8a-fcfb8ae9e948',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `I'm ready with my part. Just let me know if you need any changes.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'a87bc1b5-02cc-4af4-94a1-efeeff4ab949',
                sender: {
                    id: '10',
                    name: 'Earl Miles',
                    avatarImageUrl: '/img/avatars/thumb-10.jpg',
                },
                content: `Yes, please do. Let's all review it and meet again at 5 PM to finalize.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
        ],
    },
    {
        id: '8',
        conversation: [
            {
                id: 'b8f8f4e9-57e3-44a7-a0b8-f229a1b3c17a',
                sender: {
                    id: '13',
                    name: 'Cassandra Murray',
                    avatarImageUrl: '/img/avatars/thumb-13.jpg',
                },
                content: `Who's up for a team lunch tomorrow?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '3f9264cb-6c67-4f6e-a6b1-ef7c65b1c3e4',
                sender: {
                    id: '14',
                    name: 'Alvin Moreno',
                    avatarImageUrl: '/img/avatars/thumb-14.jpg',
                },
                content: `I'm in! Where are we thinking?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'e7a36cda-8e76-47e3-bfbc-8b09a267507f',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Count me in too. How about that new Italian place downtown?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'b3d7358d-7363-47be-8453-828e3a5ed8b4',
                sender: {
                    id: '13',
                    name: 'Cassandra Murray',
                    avatarImageUrl: '/img/avatars/thumb-13.jpg',
                },
                content: `Sounds perfect! I'll make a reservation for 1 PM.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'f6a00f9e-9083-4ccf-b79c-c85c02061cd2',
                sender: {
                    id: '14',
                    name: 'Alvin Moreno',
                    avatarImageUrl: '/img/avatars/thumb-14.jpg',
                },
                content: `Great! See you all there.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
        ],
    },
    {
        id: '9',
        conversation: [
            {
                id: '764f51fa-244f-474b-950e-6b9a03876987',
                sender: {
                    id: '15',
                    name: 'Jackie Soto',
                    avatarImageUrl: '/img/avatars/thumb-15.jpg',
                },
                content: `Guys, our camping trip is this weekend. Are we ready?`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '8511f1b7-f3e3-41f5-a3de-5184999465a3',
                sender: {
                    id: '8',
                    name: 'Jessica Wells',
                    avatarImageUrl: '/img/avatars/thumb-8.jpg',
                },
                content: `I still need to pack, but I have all the gear.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'fcf3b8a7-6e5f-469e-8100-caaef1f3f9f2',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `I'm almost ready. Just need to pick up some snacks and drinks.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: '6b399d85-5b7e-40c3-b2a4-d6888a40d44b',
                sender: {
                    id: '15',
                    name: 'Jackie Soto',
                    avatarImageUrl: '/img/avatars/thumb-15.jpg',
                },
                content: `Awesome! Let's meet at my place at 8 AM on Saturday.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: '932d5f17-fcbb-4211-8e6a-7e7d3eab1e84',
                sender: {
                    id: '8',
                    name: 'Jessica Wells',
                    avatarImageUrl: '/img/avatars/thumb-8.jpg',
                },
                content: `Sounds good to me. Can't wait!`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'a7683e7e-4cf5-45cc-9c9c-71a4f637587b',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Looking forward to it! See you all on Saturday.`,
                timestamp: 1723430400,
                type: 'regular',
                isMyMessage: true,
            },
        ],
    },
]

export const mediaData = {
    images: [
        {
            id: '7020d33d-e2d3-416a-969b-dd7e68f0e2cf',
            name: 'Snövalla',
            url: '/img/products/product-2.jpg',
        },
        {
            id: 'c20f164c-4848-4162-aa6f-0889e15a4f12',
            name: 'Spiky Ring',
            url: '/img/products/product-9.jpg',
        },
        {
            id: 'abf6c2be-3b6d-4444-9498-95a82798fcf5',
            name: 'Il Limone',
            url: '/img/products/product-12.jpg',
        },
        {
            id: '1c2502f2-326e-4bdb-b167-5e642921d2f3',
            name: 'Maneki Neko Poster',
            url: '/img/products/product-7.jpg',
        },
        {
            id: '5540ca41-b188-442b-8d2f-b4ee183b9f77',
            name: 'Gränvika',
            url: '/img/products/product-11.jpg',
        },
    ],
    files: [
        {
            id: '4a11e613-381b-4088-b189-76259ad4b0b1',
            name: 'Tech design.pdf',
            fileType: 'pdf',
            size: 2202009.6,
            srcUrl: '',
        },
        {
            id: '70fc4085-0f3a-4a19-8ee2-834cb0a49e10',
            name: 'Financial_Report.xlsx',
            fileType: 'xls',
            srcUrl: '',
            size: 1458904.3,
        },
        {
            id: '8caaa7e1-6710-45fb-8bbb-68d8d9c9ee6e',
            name: 'Project_Presentation.pptx',
            fileType: 'ppt',
            srcUrl: '',
            size: 3056789.1,
        },
        {
            id: '0cb5912f-bcc5-4840-83c4-7d96e66d612b',
            name: 'Network_Diagram.fig',
            fileType: 'figma',
            srcUrl: '',
            size: 123456.7,
        },
    ],
    links: [
        {
            id: '05604581-255a-4579-aee4-2a8e6cd9d0f5',
            favicon: '/img/thumbs/github.png',
            title: 'ThemeNate/Elstar-lite',
            description:
                'Elstar is a React admin template with developer experience friendly & highly scalable, it comes with set of UI components meticulously crafted with Tailwind CSS, it fulfilled most of the use case to create modern and beautiful UI and application',
            url: 'https://github.com/ThemeNate/Elstar-lite',
        },
        {
            id: '84fe8e78-8451-434f-b2d8-8eff16d8cc0f',
            favicon: '/img/thumbs/bootstrap.png',
            title: 'Bootstrap · The most popular HTML, CSS, and JS library in the world.',
            description:
                'Powerful, extensible, and feature-packed frontend toolkit. Build and customize with Sass, utilize prebuilt grid system and components, and bring projects to life with powerful JavaScript plugins.',
            url: 'https://getbootstrap.com/',
        },
        {
            id: '4df30543-36cd-47fe-97fe-790dcb5f16fe',
            favicon: '/img/thumbs/tailwind.png',
            title: 'Tailwind CSS - Rapidly build modern websites without ever leaving your HTML.',
            description:
                'Tailwind CSS is a utility-first CSS framework for rapidly building modern websites without ever leaving your HTML.',
            url: 'https://getbootstrap.com/',
        },
    ],
}

export const groupsData = [
    {
        id: '16',
        name: 'Team Presentation Prep',
        img: '/img/others/img-19.jpg',
        members: [
            {
                id: '1',
                name: 'You',
                img: '/img/avatars/thumb-1.jpg',
                email: '<EMAIL>',
            },
            {
                id: '10',
                name: 'Earl Miles',
                img: '/img/avatars/thumb-10.jpg',
                email: '<EMAIL>',
            },
            {
                id: '11',
                name: 'Steve Sutton',
                img: '/img/avatars/thumb-11.jpg',
                email: '<EMAIL>',
            },
            {
                id: '12',
                name: 'Miriam Herrera',
                img: '/img/avatars/thumb-12.jpg',
                email: '<EMAIL>',
            },
        ],
    },
    {
        id: '17',
        name: 'Lunch Squad',
        img: '/img/others/img-18.jpg',
        members: [
            {
                id: '1',
                name: 'You',
                img: '/img/avatars/thumb-1.jpg',
                email: '<EMAIL>',
            },
            {
                id: '13',
                name: 'Cassandra Murray',
                img: '/img/avatars/thumb-13.jpg',
                email: '<EMAIL>',
            },
            {
                id: '14',
                name: 'Alvin Moreno',
                img: '/img/avatars/thumb-14.jpg',
                email: '<EMAIL>',
            },
        ],
    },
    {
        id: '18',
        name: 'Camping Crew',
        img: '/img/others/img-17.jpg',
        members: [
            {
                id: '1',
                name: 'You',
                img: '/img/avatars/thumb-1.jpg',
                email: '<EMAIL>',
            },
            {
                id: '15',
                name: 'Jackie Soto',
                img: '/img/avatars/thumb-15.jpg',
                email: '<EMAIL>',
            },
            {
                id: '8',
                name: 'Jessica Wells',
                img: '/img/avatars/thumb-8.jpg',
                email: '<EMAIL>',
            },
        ],
    },
]
