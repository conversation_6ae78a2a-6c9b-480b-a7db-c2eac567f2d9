export const categoriesData = [
    {
        name: 'Get Started',
        topics: [
            {
                id: 'introduction',
                name: 'Introduction',
                description:
                    'Guides for new users to get familiar with the system.',
                articleCounts: 4,
            },
            {
                id: 'setupGuide',
                name: 'Setup Guide',
                description:
                    'Step-by-step instructions for installing the software or system.',
                articleCounts: 6,
            },
            {
                id: 'basicFeatures',
                name: 'Basic Features',
                description:
                    'Introduction to the basic features and functionalities.',
                articleCounts: 3,
            },
        ],
    },
    {
        name: 'Data Collection and Analysis',
        topics: [
            {
                id: 'survey',
                name: 'Survey',
                description:
                    'Articles related to collecting data through surveys.',
                articleCounts: 4,
            },
            {
                id: 'analytic',
                name: 'Analytic',
                description: 'Articles related to analyzing data.',
                articleCounts: 6,
            },
            {
                id: 'dataVisualization',
                name: 'Data Visualization',
                description:
                    'Articles related to visualizing data in charts, graphs, etc.',
                articleCounts: 9,
            },
        ],
    },
    {
        name: 'System Management and Security',
        topics: [
            {
                id: 'chatbot',
                name: 'Chat<PERSON>',
                description: 'Articles related to automated communication.',
                articleCounts: 8,
            },
            {
                id: 'media',
                name: 'Media',
                description: ' Articles related to media and content.',
                articleCounts: 3,
            },
            {
                id: 'security',
                name: 'Security',
                description: 'Articles related to ensuring security.',
                articleCounts: 5,
            },
            {
                id: 'integration',
                name: 'Integration',
                description:
                    'Articles related to integrating different systems.',
                articleCounts: 7,
            },
            {
                id: 'themes',
                name: 'Themes',
                description: ' Articles related to themes and customization.',
                articleCounts: 9,
            },
            {
                id: 'commission',
                name: 'Commission',
                description: 'Articles related to managing commissions.',
                articleCounts: 11,
            },
        ],
    },
]

export const articleListData = [
    {
        id: 'pWBKE_0UiQ',
        title: 'Things you need to know about configuring the theme',
        content:
            'Measuring programming progress by lines of code is like measuring aircraft building progress by weight. You cant have great software without a great team, and most software teams behave like dysfunctional families.',
        category: 'themes',
        authors: [
            {
                name: 'Ron Vargas',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        tags: [
            { id: 2, label: 'Implementation' },
            { id: 3, label: 'Troubleshooting' },
        ],
        starred: true,
        published: true,
        updateTime: '2 weeks ago',
        updateTimeStamp: 1717830464,
        createdBy: 'Ron Vargas',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 42,
    },
    {
        id: '2tv0CboXfj',
        title: 'Understand the background of themes',
        content:
            'A hacker on a roll may be able to produce-in a period of a few months-something that a small development group (say, 7-8 people) would have a hard time getting together over a year. IBM used to report that certain programmers might be as much as 100 times as productive as other workers, or more.',
        category: 'themes',
        authors: [
            {
                name: 'Carolyn Hanson',
                img: '/img/avatars/thumb-9.jpg',
            },
            {
                name: 'Samantha Phillips',
                img: '/img/avatars/thumb-6.jpg',
            },
        ],
        tags: [
            { id: 6, label: 'Integration' },
            { id: 7, label: 'Optimization' },
            { id: 8, label: 'Security' },
        ],
        starred: true,
        published: true,
        updateTime: '6 months ago',
        updateTimeStamp: 1703488064,
        createdBy: 'Carolyn Hanson',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 12,
    },
    {
        id: 'XHpEwVPi4D',
        title: 'Basic tools you need to know on configuring the theme',
        content:
            'Its ridiculous to live 100 years and only be able to remember 30 million bytes. You know, less than a compact disc.  The human condition is really becoming more obsolete every minute.',
        category: 'themes',
        authors: [
            {
                name: 'Lloyd Obrien',
                img: '/img/avatars/thumb-11.jpg',
            },
        ],
        tags: [
            { id: 2, label: 'Implementation' },
            { id: 3, label: 'Troubleshooting' },
        ],
        starred: false,
        published: false,
        updateTime: '4 months ago',
        updateTimeStamp: 1708672064,
        createdBy: 'Lloyd Obrien',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 12,
    },
    {
        id: '09R6gIH5zD',
        title: 'Simple guidance for you in theming',
        content:
            'As soon as we started programming, we found to our surprise that it wasnt as easy to get programs right as we had thought. Debugging had to be discovered.  I can remember the exact instant when I realized that a large part of my life from then on was going to be spent in finding mistakes in my own programs.',
        category: 'themes',
        authors: [
            {
                name: 'Joyce Freeman',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        tags: [
            { id: 9, label: 'Deployment' },
            { id: 10, label: 'Maintenance' },
            { id: 11, label: 'Scalability' },
            { id: 12, label: 'Performance' },
            { id: 13, label: 'Backup' },
        ],
        starred: false,
        published: true,
        updateTime: '3 months ago',
        updateTimeStamp: **********,
        createdBy: 'Joyce Freeman',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 53,
    },
    {
        id: 'rZjCbSyae5',
        title: 'How to create a survey form',
        content:
            'Come to think of it, there are already a million monkeys on a million typewriters, and Usenet is nothing like Shakespeare',
        category: 'survey',
        authors: [
            {
                name: 'Terrance Moreno',
                img: '/img/avatars/thumb-2.jpg',
            },
        ],
        tags: [
            { id: 14, label: 'Monitoring' },
            { id: 15, label: 'Automation' },
            { id: 16, label: 'Testing' },
            { id: 17, label: 'Migration' },
        ],
        starred: true,
        published: true,
        updateTime: '3 months ago',
        updateTimeStamp: **********,
        createdBy: 'Terrance Moreno',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 2,
    },
    {
        id: 'mmPo6vqS1t',
        title: 'Survey form best practice',
        content:
            'Come to think of it, there are already a million monkeys on a million typewriters, and Usenet is nothing like Shakespeare',
        category: 'survey',
        authors: [
            {
                name: 'Luke Cook',
                img: '/img/avatars/thumb-4.jpg',
            },
            {
                name: 'Samantha Phillips',
                img: '/img/avatars/thumb-6.jpg',
            },
            {
                name: 'Joyce Freeman',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        tags: [
            { id: 18, label: 'Logging' },
            { id: 19, label: 'Recovery' },
        ],
        starred: false,
        published: false,
        updateTime: 'Last week',
        updateTimeStamp: 1718435264,
        createdBy: 'Luke Cook',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 4,
    },
    {
        id: '6ETEWm8ZHt',
        title: 'Precious tips to help you create a better question',
        content:
            'Computer science education cannot make anybody an expert programmer any more than studying brushes and pigment can make somebody an expert painter. (Eric Raymond) The Internet? Is that thing still around?',
        category: 'survey',
        authors: [
            {
                name: 'Joyce Freeman',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        tags: [
            { id: 14, label: 'Monitoring' },
            { id: 3, label: 'Troubleshooting' },
            { id: 8, label: 'Security' },
        ],
        starred: false,
        published: false,
        updateTime: '2 months ago',
        updateTimeStamp: **********,
        createdBy: 'Joyce Freeman',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: 'WQCy5CZcEJ',
        title: 'Quick tips regarding security',
        content:
            'It would appear that we have reached the limits of what it is possible to achieve with computer technology, although one should be careful with such statements, as they tend to sound pretty silly in 5 years.',
        category: 'security',
        authors: [
            {
                name: 'Tara Fletcher',
                img: '/img/avatars/thumb-7.jpg',
            },
        ],
        tags: [
            { id: 1, label: 'Configuring' },
            { id: 7, label: 'Optimization' },
            { id: 10, label: 'Maintenance' },
        ],
        starred: false,
        published: true,
        updateTime: '4 months ago',
        updateTimeStamp: 1708672064,
        createdBy: 'Tara Fletcher',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 1,
    },
    {
        id: 'prUH65MCWD',
        title: 'All You need to know about privacy setting',
        content:
            'As soon as we started programming, we found to our surprise that it wasnt as easy to get programs right as we had thought. Debugging had to be discovered.  I can remember the exact instant when I realized that a large part of my life from then on was going to be spent in finding mistakes in my own programs.',
        category: 'security',
        authors: [
            {
                name: 'Tara Fletcher',
                img: '/img/avatars/thumb-7.jpg',
            },
        ],
        tags: [
            { id: 4, label: 'Customization' },
            { id: 11, label: 'Scalability' },
            { id: 5, label: 'Setup' },
            { id: 15, label: 'Automation' },
        ],
        starred: false,
        published: false,
        updateTime: '8 months ago',
        updateTimeStamp: 1698304064,
        createdBy: 'Tara Fletcher',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 2,
    },
    {
        id: '1xK5PJRunm',
        title: 'Simple guide for integration',
        content:
            'Software undergoes beta testing shortly before its released. Beta is Latin for “still doesnt work”. (Anonymous) Come to think of it, there are already a million monkeys on a million typewriters, and Usenet is nothing like Shakespeare. Deleted code is debugged code. ',
        category: 'integration',
        authors: [
            {
                name: 'Frederick Adams',
                img: '/img/avatars/thumb-8.jpg',
            },
        ],
        tags: [
            { id: 3, label: 'Troubleshooting' },
            { id: 8, label: 'Security' },
        ],
        starred: false,
        published: true,
        updateTime: '7 months ago',
        updateTimeStamp: 1700896064,
        createdBy: 'Frederick Adams',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 3,
    },
    {
        id: 'iVB_L9xy2d',
        title: 'Most effective ways to overcome integration issue.',
        content:
            'A programmer is a person who passes as an exacting expert on the basis of being able to turn out, after innumerable punching, an infinite series of incomprehensive answers calculated with micrometric precisions from vague assumptions based on debatable figures taken from inconclusive documents and carried out on instruments of problematical accuracy by persons of dubious reliability and questionable mentality for the avowed purpose of annoying and confounding a hopelessly defenseless department that was unfortunate enough to ask for the information in the first place.',
        category: 'integration',
        authors: [
            {
                name: 'Gabriella May',
                img: '/img/avatars/thumb-12.jpg',
            },
            {
                name: 'Gail Barnes',
                img: '/img/avatars/thumb-14.jpg',
            },
        ],
        tags: [
            { id: 2, label: 'Implementation' },
            { id: 6, label: 'Integration' },
            { id: 12, label: 'Performance' },
            { id: 16, label: 'Testing' },
        ],
        starred: false,
        published: true,
        updateTime: '3 days ago',
        updateTimeStamp: 1718780864,
        createdBy: 'Gabriella May',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 1,
    },
    {
        id: 't6D3gCV8hN',
        title: 'API document for integration',
        content:
            'For a long time it puzzled me how something so expensive, so leading edge, could be so useless. And then it occurred to me that a computer is a stupid machine with the ability to do incredibly smart things, while computer programmers are smart people with the ability to do incredibly stupid things.  They are, in short, a perfect match',
        category: 'integration',
        authors: [
            {
                name: 'Ella Robinson',
                img: '/img/avatars/thumb-15.jpg',
            },
        ],
        tags: [
            { id: 9, label: 'Deployment' },
            { id: 17, label: 'Migration' },
            { id: 13, label: 'Backup' },
            { id: 18, label: 'Logging' },
            { id: 19, label: 'Recovery' },
        ],
        starred: false,
        published: true,
        updateTime: '2 weeks ago',
        updateTimeStamp: 1717830464,
        createdBy: 'Gabriella May',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 31,
    },
    {
        id: 'gI3fhHZzoQ',
        title: 'Integration best practice',
        content:
            'Microsoft has a new version out, Windows XP, which according to everybody is the most reliable Windows ever. To me, this is like saying that asparagus is the most articulate vegetable ever. ',
        category: 'integration',
        authors: [
            {
                name: 'Lee Wheeler',
                img: '/img/avatars/thumb-13.jpg',
            },
        ],
        tags: [
            { id: 14, label: 'Monitoring' },
            { id: 2, label: 'Implementation' },
            { id: 5, label: 'Setup' },
        ],
        starred: false,
        published: true,
        updateTime: '3 weeks ago',
        updateTimeStamp: 1717225664,
        createdBy: 'Lee Wheeler',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 7,
    },
    {
        id: '4zu-IVncIh',
        title: 'Managing media storage',
        content:
            'The citys central computer told you? R2D2, you know better than to trust a strange computer! (C3PO) 640K ought to be enough for anybody.',
        category: 'media',
        authors: [
            {
                name: 'Brittany Hale',
                img: '/img/avatars/thumb-10.jpg',
            },
        ],
        tags: [
            { id: 11, label: 'Scalability' },
            { id: 18, label: 'Logging' },
        ],
        starred: false,
        published: true,
        updateTime: 'a year ago',
        updateTimeStamp: **********,
        createdBy: 'Brittany Hale',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 9,
    },
    {
        id: '4zu-WQX60M9MDJ',
        title: 'Beware of oversized',
        content:
            'One mans crappy software is another mans full-time job. (Jessica Gaston) Its a curious thing about our industry: not only do we not learn from our mistakes, but we also dont learn from our successes.',
        category: 'media',
        authors: [
            {
                name: 'Terrance Moreno',
                img: '/img/avatars/thumb-2.jpg',
            },
            {
                name: 'Brittany Hale',
                img: '/img/avatars/thumb-10.jpg',
            },
        ],
        tags: [
            { id: 3, label: 'Troubleshooting' },
            { id: 13, label: 'Backup' },
            { id: 7, label: 'Optimization' },
            { id: 4, label: 'Customization' },
        ],
        starred: false,
        published: true,
        updateTime: 'a year ago',
        updateTimeStamp: **********,
        createdBy: 'Terrance Moreno',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: 'yHE5vZ4sow',
        title: 'Several effective ways to optimize media file',
        content:
            'Always code as if the guy who ends up maintaining your code will be a violent psychopath who knows where you live. Come to think of it, there are already a million monkeys on a million typewriters, and Usenet is nothing like Shakespeare.',
        category: 'media',
        authors: [
            {
                name: 'Ron Vargas',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        tags: [
            { id: 14, label: 'Monitoring' },
            { id: 3, label: 'Troubleshooting' },
            { id: 8, label: 'Security' },
        ],
        starred: false,
        published: false,
        updateTime: '9 months ago',
        updateTimeStamp: 1695712064,
        createdBy: 'Ron Vargas',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: 'CZZxidbzLi',
        title: 'Collecting data like a charm',
        content:
            'Programming is like sex: one mistake and youre providing support for a lifetime. It would appear that we have reached the limits of what it is possible to achieve with computer technology, although one should be careful with such statements, as they tend to sound pretty silly in 5 years.',
        category: 'analytic',
        authors: [
            {
                name: 'Luke Cook',
                img: '/img/avatars/thumb-4.jpg',
            },
        ],
        tags: [
            { id: 6, label: 'Integration' },
            { id: 15, label: 'Automation' },
        ],
        starred: false,
        published: true,
        updateTime: '3 months ago',
        updateTimeStamp: **********,
        createdBy: 'Luke Cook',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: 'aOuPn7DxZz',
        title: 'Simple guide for snapshot report',
        content:
            'If builders built buildings the way programmers wrote programs, then the first woodpecker that came along would destroy civilization. There are only two industries that refer to their customers as users',
        category: 'analytic',
        authors: [
            {
                name: 'Carolyn Hanson',
                img: '/img/avatars/thumb-9.jpg',
            },
        ],
        tags: [
            { id: 8, label: 'Security' },
            { id: 16, label: 'Testing' },
            { id: 1, label: 'Configuring' },
            { id: 19, label: 'Recovery' },
            { id: 12, label: 'Performance' },
        ],
        starred: false,
        published: false,
        updateTime: '4 months ago',
        updateTimeStamp: 1708672064,
        createdBy: 'Carolyn Hanson',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: '90hXaTVMr3',
        title: 'Several ways to generate comparison report',
        content:
            'The trouble with programmers is that you can never tell what a programmer is doing until its too late. Most of you are familiar with the virtues of a programmer. There are three, of course: laziness, impatience, and hubris.  In order to understand recursion, one must first understand recursion.',
        category: 'analytic',
        authors: [
            {
                name: 'Carolyn Perkins',
                img: '/img/avatars/thumb-1.jpg',
            },
        ],
        tags: [
            { id: 9, label: 'Deployment' },
            { id: 17, label: 'Migration' },
            { id: 10, label: 'Maintenance' },
        ],
        starred: false,
        published: true,
        updateTime: '10 months ago',
        updateTimeStamp: 1693120064,
        createdBy: 'Eileen Horton',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: 'bd3Hxes4NB',
        title: 'Add new customer service on chatbot',
        content:
            'It would appear that we have reached the limits of what it is possible to achieve with computer technology, although one should be careful with such statements, as they tend to sound pretty silly in 5 years. ',
        category: 'chatbot',
        authors: [
            {
                name: 'Terrance Moreno',
                img: '/img/avatars/thumb-2.jpg',
            },
        ],
        tags: [
            { id: 11, label: 'Scalability' },
            { id: 2, label: 'Implementation' },
            { id: 18, label: 'Logging' },
        ],
        starred: false,
        published: false,
        updateTime: '2 months ago',
        updateTimeStamp: **********,
        createdBy: 'Terrance Moreno',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: 'zcZn3PBqKf',
        title: 'Configuring auto reply',
        content:
            'The function of good software is to make the complex appear to be simple. If the automobile had followed the same development cycle as the computer, a Rolls-Royce would today cost $100, get a million miles per gallon, and explode once a year, killing everyone inside.',
        category: 'chatbot',
        authors: [
            {
                name: 'Frederick Adams',
                img: '/img/avatars/thumb-8.jpg',
            },
        ],
        tags: [
            { id: 1, label: 'Configuring' },
            { id: 9, label: 'Deployment' },
            { id: 15, label: 'Automation' },
            { id: 5, label: 'Setup' },
        ],
        starred: false,
        published: true,
        updateTime: '5 months ago',
        updateTimeStamp: **********,
        createdBy: 'Frederick Adams',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 10,
    },
    {
        id: 'LWy8fEP-tA',
        title: 'Commission calculation',
        content:
            'In software, we rarely have meaningful requirements. Even if we do, the only measure of success that matters is whether our solution solves the customers shifting idea of what their problem is. The best method for accelerating a computer is the one that boosts it by 9.8 m/s2',
        category: 'commission',
        authors: [
            {
                name: 'Lee Wheeler',
                img: '/img/avatars/thumb-13.jpg',
            },
        ],
        tags: [
            { id: 14, label: 'Monitoring' },
            { id: 3, label: 'Troubleshooting' },
            { id: 8, label: 'Security' },
        ],
        starred: false,
        published: false,
        updateTime: '5 days ago',
        updateTimeStamp: 1718608064,
        createdBy: 'Lee Wheeler',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 12,
    },
    {
        id: 'SAxrgvMuip',
        title: 'Export commission statement',
        content:
            'One mans crappy software is another mans full-time job. Its a curious thing about our industry: not only do we not learn from our mistakes, but we also dont learn from our successes. Measuring programming progress by lines of code is like measuring aircraft building progress by weight',
        category: 'commission',
        authors: [
            {
                name: 'Joyce Freeman',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        tags: [{ id: 6, label: 'Integration' }],
        starred: false,
        published: true,
        updateTime: 'a year ago',
        updateTimeStamp: **********,
        createdBy: 'Joyce Freeman',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 4,
    },
    {
        id: 'NcUFsNDmUV',
        title: 'Commission policy',
        content:
            'Fine, Java MIGHT be a good example of what a programming language should be like. But Java applications are good examples of what applications SHOULDN’T be like. (pixadel) Fifty years of programming language research, and we end up with C++? ',
        category: 'commission',
        authors: [
            {
                name: 'Carolyn Perkins',
                img: '/img/avatars/thumb-1.jpg',
            },
            {
                name: 'Gabriella May',
                img: '/img/avatars/thumb-12.jpg',
            },
        ],
        tags: [
            { id: 17, label: 'Migration' },
            { id: 12, label: 'Performance' },
        ],
        starred: false,
        published: true,
        updateTime: 'a year ago',
        updateTimeStamp: **********,
        createdBy: 'Eileen Horton',
        timeToRead: 2,
        viewCount: 37,
        commentCount: 0,
    },
    {
        id: 'Dv1JsNEkWU',
        title: 'The Power of Data Visualization',
        content:
            'Data visualization is crucial for making complex data understandable and actionable. In this article, we explore various techniques and tools to transform raw data into insightful visuals.',
        category: 'dataVisualization',
        authors: [
            {
                name: 'Alex Johnson',
                img: '/img/avatars/thumb-2.jpg',
            },
            {
                name: 'Emily Zhang',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        tags: [
            { id: 10, label: 'Maintenance' },
            { id: 7, label: 'Optimization' },
        ],
        starred: false,
        published: true,
        updateTime: '3 months ago',
        updateTimeStamp: **********,
        createdBy: 'Michael Green',
        timeToRead: 5,
        viewCount: 85,
        commentCount: 0,
    },
    {
        id: 'Ks2DfNEmUZ',
        title: 'Top Tools for Data Visualization in 2024',
        content:
            'Staying up-to-date with the best data visualization tools is essential for any data analyst. This article reviews the top tools available in 2024, highlighting their features and benefits.',
        category: 'dataVisualization',
        authors: [
            {
                name: 'Olivia Brown',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        tags: [{ id: 3, label: 'Troubleshooting' }],
        starred: false,
        published: false,
        updateTime: '1 month ago',
        updateTimeStamp: 1716448064,
        createdBy: 'Ethan Lewis',
        timeToRead: 4,
        viewCount: 120,
        commentCount: 0,
    },
    {
        id: 'Ms3RtNPlUZ',
        title: 'Creating Interactive Dashboards',
        content:
            'Interactive dashboards can significantly enhance data comprehension and engagement. This guide walks you through the process of creating dynamic and interactive dashboards using popular tools.',
        category: 'dataVisualization',
        authors: [
            {
                name: 'Sophia Martinez',
                img: '/img/avatars/thumb-4.jpg',
            },
            {
                name: 'James Lee',
                img: '/img/avatars/thumb-9.jpg',
            },
        ],
        tags: [
            { id: 6, label: 'Integration' },
            { id: 13, label: 'Backup' },
            { id: 17, label: 'Migration' },
            { id: 12, label: 'Performance' },
        ],
        starred: false,
        published: true,
        updateTime: '2 weeks ago',
        updateTimeStamp: 1717830464,
        createdBy: 'Daniel Robinson',
        timeToRead: 7,
        viewCount: 95,
        commentCount: 54,
    },
    {
        id: 'Jh5VsNQlUZ',
        title: 'Data Storytelling with Visualization',
        content:
            'Data storytelling combines data, visuals, and narrative to tell compelling stories. Learn how to craft engaging stories using data visualization techniques in this comprehensive guide.',
        category: 'dataVisualization',
        authors: [
            {
                name: 'Isabella Clark',
                img: '/img/avatars/thumb-7.jpg',
            },
            {
                name: 'William Davis',
                img: '/img/avatars/thumb-10.jpg',
            },
        ],
        tags: [
            { id: 4, label: 'Customization' },
            { id: 10, label: 'Maintenance' },
            { id: 7, label: 'Optimization' },
            { id: 16, label: 'Testing' },
            { id: 19, label: 'Recovery' },
        ],
        starred: false,
        published: false,
        updateTime: '6 months ago',
        updateTimeStamp: 1703488064,
        createdBy: 'Sophia Martinez',
        timeToRead: 8,
        viewCount: 110,
        commentCount: 67,
    },
    {
        id: 'Ia6XsNPtVW',
        title: 'Welcome to Our Platform',
        content:
            "This article provides an overview of our platform, highlighting its main features and functionalities. It's the perfect starting point for new users.",
        category: 'introduction',
        authors: [
            {
                name: 'John Smith',
                img: '/img/avatars/thumb-8.jpg',
            },
            {
                name: 'Sarah Brown',
                img: '/img/avatars/thumb-11.jpg',
            },
        ],
        tags: [
            { id: 8, label: 'Security' },
            { id: 1, label: 'Configuring' },
            { id: 12, label: 'Performance' },
        ],
        starred: false,
        published: true,
        updateTime: '1 year ago',
        updateTimeStamp: **********,
        createdBy: 'Rachel Green',
        timeToRead: 3,
        viewCount: 150,
        commentCount: 10,
    },
    {
        id: 'Bb7YsNQmUV',
        title: "Getting Started: A Beginner's Guide",
        content:
            "This beginner's guide will help you get up and running with our platform. Learn how to set up your account and start using our basic features.",
        category: 'introduction',
        authors: [
            {
                name: 'Michael Johnson',
                img: '/img/avatars/thumb-9.jpg',
            },
        ],
        tags: [{ id: 8, label: 'Security' }],
        starred: true,
        published: true,
        updateTime: '2 months ago',
        updateTimeStamp: **********,
        createdBy: 'David Parker',
        timeToRead: 5,
        viewCount: 200,
        commentCount: 43,
    },
    {
        id: 'Cc8ZsNRnWX',
        title: 'Understanding the Dashboard',
        content:
            'This article explains the various components of the dashboard, helping new users to navigate and utilize its features effectively.',
        category: 'introduction',
        authors: [
            {
                name: 'Emma Davis',
                img: '/img/avatars/thumb-10.jpg',
            },
            {
                name: 'James Wilson',
                img: '/img/avatars/thumb-12.jpg',
            },
        ],
        tags: [
            { id: 16, label: 'Testing' },
            { id: 1, label: 'Configuring' },
        ],
        starred: false,
        published: true,
        updateTime: '6 months ago',
        updateTimeStamp: 1703488064,
        createdBy: 'Chris Martin',
        timeToRead: 4,
        viewCount: 175,
        commentCount: 10,
    },
    {
        id: 'Dd9AsNStXY',
        title: 'Introduction to Key Features',
        content:
            'Learn about the key features of our platform in this detailed introduction. We cover everything from basic tools to advanced functionalities.',
        category: 'introduction',
        authors: [
            {
                name: 'Olivia Taylor',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        tags: [
            { id: 2, label: 'Implementation' },
            { id: 6, label: 'Integration' },
            { id: 12, label: 'Performance' },
        ],
        starred: false,
        published: true,
        updateTime: '4 months ago',
        updateTimeStamp: 1708672064,
        createdBy: 'Sophia Clark',
        timeToRead: 6,
        viewCount: 220,
        commentCount: 10,
    },
    {
        id: 'Ee0BsNQuYZ',
        title: 'New User Orientation',
        content:
            'This orientation guide is designed to help new users quickly get familiar with our platform. It covers essential tips and tricks for beginners.',
        category: 'introduction',
        authors: [
            {
                name: 'William Robinson',
                img: '/img/avatars/thumb-4.jpg',
            },
            {
                name: 'Isabella Martinez',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        tags: [
            { id: 4, label: 'Customization' },
            { id: 11, label: 'Scalability' },
        ],
        starred: true,
        published: true,
        updateTime: '1 week ago',
        updateTimeStamp: 1718435264,
        createdBy: 'Emma Davis',
        timeToRead: 7,
        viewCount: 300,
        commentCount: 21,
    },
    {
        id: 'Fg1WsNQuXY',
        title: 'Step-by-Step Installation Guide',
        content:
            'This guide provides a detailed, step-by-step process for installing our platform on your system. It includes screenshots and troubleshooting tips to ensure a smooth installation.',
        category: 'setupGuide',
        authors: [
            {
                name: 'John Doe',
                img: '/img/avatars/thumb-1.jpg',
            },
        ],
        tags: [
            { id: 5, label: 'Setup' },
            { id: 15, label: 'Automation' },
        ],
        starred: true,
        published: true,
        updateTime: '2 weeks ago',
        updateTimeStamp: 1717830464,
        createdBy: 'Jane Smith',
        timeToRead: 10,
        viewCount: 250,
        commentCount: 30,
    },
    {
        id: 'Gh2XsNPvYZ',
        title: 'Initial Configuration Settings',
        content:
            'Learn how to configure the initial settings of our platform. This article covers essential configurations to get you started quickly and efficiently.',
        category: 'setupGuide',
        authors: [
            {
                name: 'Emily Watson',
                img: '/img/avatars/thumb-2.jpg',
            },
            {
                name: 'Mark Johnson',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        tags: [{ id: 9, label: 'Deployment' }],
        starred: false,
        published: false,
        updateTime: '1 month ago',
        updateTimeStamp: 1716448064,
        createdBy: 'Chris Evans',
        timeToRead: 8,
        viewCount: 180,
        commentCount: 22,
    },
    {
        id: 'Ij3YsNQwXZ',
        title: 'Network Setup and Configuration',
        content:
            'This guide helps you set up and configure the network settings required for our platform. It includes detailed instructions and best practices for network optimization.',
        category: 'setupGuide',
        authors: [
            {
                name: 'Olivia Brown',
                img: '/img/avatars/thumb-4.jpg',
            },
        ],
        tags: [
            { id: 18, label: 'Logging' },
            { id: 19, label: 'Recovery' },
        ],
        starred: true,
        published: true,
        updateTime: '3 months ago',
        updateTimeStamp: **********,
        createdBy: 'Liam White',
        timeToRead: 12,
        viewCount: 220,
        commentCount: 18,
    },
    {
        id: 'Kl4ZsNQtYZ',
        title: 'User Account Setup',
        content:
            'This article provides a comprehensive guide to setting up user accounts on our platform. It covers account creation, role assignment, and permission settings.',
        category: 'setupGuide',
        authors: [
            {
                name: 'Sophia Lee',
                img: '/img/avatars/thumb-5.jpg',
            },
            {
                name: 'James Wilson',
                img: '/img/avatars/thumb-6.jpg',
            },
        ],
        tags: [{ id: 13, label: 'Backup' }],
        starred: false,
        published: false,
        updateTime: '5 months ago',
        updateTimeStamp: **********,
        createdBy: 'Emma Davis',
        timeToRead: 9,
        viewCount: 200,
        commentCount: 25,
    },
    {
        id: 'Lm5WsNQuXY',
        title: 'Navigating the Dashboard',
        content:
            'This article explains how to navigate the main dashboard of our platform. Learn about the various sections and how to access key features easily.',
        category: 'basicFeatures',
        authors: [
            {
                name: 'Alice Johnson',
                img: '/img/avatars/thumb-1.jpg',
            },
        ],
        tags: [{ id: 17, label: 'Migration' }],
        starred: false,
        published: true,
        updateTime: '3 weeks ago',
        updateTimeStamp: 1717225664,
        createdBy: 'David Lee',
        timeToRead: 5,
        viewCount: 389,
        commentCount: 14,
    },
    {
        id: 'No6XsNPrYZ',
        title: 'Using the Search Function',
        content:
            'Learn how to use the powerful search function of our platform to find the information you need quickly and efficiently. This guide covers basic and advanced search techniques.',
        category: 'basicFeatures',
        authors: [
            {
                name: 'Michael Brown',
                img: '/img/avatars/thumb-2.jpg',
            },
            {
                name: 'Laura White',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        tags: [{ id: 9, label: 'Deployment' }],
        starred: true,
        published: true,
        updateTime: '1 month ago',
        updateTimeStamp: 1716448064,
        createdBy: 'Samantha Green',
        timeToRead: 4,
        viewCount: 457,
        commentCount: 19,
    },
    {
        id: 'Op7YsNRqXZ',
        title: 'Customizing Your Profile',
        content:
            'This guide walks you through the process of customizing your profile. Learn how to update your personal information, change your avatar, and set your preferences.',
        category: 'basicFeatures',
        authors: [
            {
                name: 'Emily Davis',
                img: '/img/avatars/thumb-4.jpg',
            },
        ],
        tags: [
            { id: 2, label: 'Implementation' },
            { id: 5, label: 'Setup' },
        ],
        starred: false,
        published: true,
        updateTime: '2 months ago',
        updateTimeStamp: **********,
        createdBy: 'John Smith',
        timeToRead: 6,
        viewCount: 498,
        commentCount: 23,
    },
    {
        id: 'Qq8ZsNQtYZ',
        title: 'Basic Reporting Features',
        content:
            'Discover the basic reporting features available on our platform. This article covers how to generate, view, and export reports to gain insights from your data.',
        category: 'basicFeatures',
        authors: [
            {
                name: 'Sophia Lee',
                img: '/img/avatars/thumb-5.jpg',
            },
            {
                name: 'James Wilson',
                img: '/img/avatars/thumb-6.jpg',
            },
        ],
        tags: [{ id: 5, label: 'Setup' }],
        starred: false,
        published: false,
        updateTime: '5 months ago',
        updateTimeStamp: **********,
        createdBy: 'Emma Davis',
        timeToRead: 7,
        viewCount: 512,
        commentCount: 27,
    },
]

export const articleDetailData = {
    content: `
    <p>Technology is evolving at an unprecedented pace, transforming every aspect of our lives. From artificial intelligence to quantum computing, the advancements in technology are opening up new possibilities and reshaping industries across the globe.</p>
    <div id="artificialIntelligence">
        <h5>Artificial Intelligence</h5><p>Artificial Intelligence (AI) is one of the most significant technological advancements of our time. AI is being integrated into various sectors such as healthcare, finance, and transportation, offering innovative solutions to complex problems. In the future, AI is expected to further revolutionize industries by improving efficiency, accuracy, and decision-making processes.</p><p>In healthcare, AI is being used to develop personalized treatment plans, improve diagnostic accuracy, and enhance patient care. For instance, AI algorithms can analyze vast amounts of medical data to identify patterns and predict disease outbreaks. In finance, AI is transforming trading strategies, risk management, and customer service through advanced data analytics and automated systems.</p> </div>
    <div id="quantumComputing">
        <h5 >Quantum Computing</h5><p>Quantum computing is another groundbreaking technology that holds the potential to solve problems that are currently unsolvable by classical computers. With the ability to perform complex calculations at unprecedented speeds, quantum computers could transform fields such as cryptography, materials science, and pharmaceuticals.</p><p>Researchers are already exploring the use of quantum computing to design new materials with specific properties, optimize supply chains, and develop new drug therapies. The sheer computational power of quantum computers could also revolutionize machine learning, leading to more advanced AI systems and deeper insights from data.</p>
    </div>
    <div id="internetOfThings">
    <h5 >Internet of Things (IoT)</h5>
    <p>The Internet of Things (IoT) is connecting devices and systems like never before, creating a more interconnected world. From smart homes to industrial automation, IoT is enhancing the way we live and work by providing real-time data and insights. As IoT technology continues to evolve, it is expected to bring even more innovative applications and efficiencies.</p><p>Smart cities are emerging as a result of IoT, where interconnected systems manage everything from traffic flow to energy usage, improving the quality of urban life. In agriculture, IoT devices monitor soil conditions, weather patterns, and crop health, leading to more efficient farming practices and higher yields.</p>
    </div>
    <div id="5gTechnology">
        <h5>5G Technology</h5><p>5G technology is set to revolutionize the way we communicate and access information. With faster speeds and lower latency, 5G will enable new applications such as autonomous vehicles, smart cities, and advanced healthcare solutions. The widespread adoption of 5G is expected to drive economic growth and create new opportunities across various industries.</p><p>In addition to enhancing mobile communications, 5G will support the growth of IoT by providing the necessary infrastructure for millions of connected devices. This will lead to the development of innovative applications in various sectors, including remote surgery, real-time language translation, and immersive virtual reality experiences.</p>
    </div>
    <div id="blockchainTechnology">
    <h5>Blockchain Technology</h5><p>Blockchain technology, originally developed as the backbone of cryptocurrencies like Bitcoin, is now being recognized for its potential to revolutionize various industries beyond finance. By providing a decentralized and secure way to record transactions, blockchain technology can enhance transparency, security, and efficiency.</p><p>In supply chain management, blockchain can track products from origin to delivery, ensuring authenticity and reducing fraud. In healthcare, it can secure patient records, enabling better data sharing while maintaining privacy. Furthermore, smart contracts, which are self-executing contracts with the terms directly written into code, can automate and streamline complex legal and financial agreements.</p><p>Blockchain’s potential extends to voting systems, where it can ensure transparency and prevent tampering, thereby increasing trust in the electoral process. As the technology matures, its applications are expected to expand, driving innovation and efficiency in numerous fields.</p><p>As we look to the future, it is clear that technology will continue to play a crucial role in shaping our world. The advancements in AI, quantum computing, IoT, 5G, and blockchain are just the beginning. Embracing these technologies and leveraging their potential will be key to driving innovation and progress in the years to come.</p>
    </div>`,
    tableOfContent: [
        {
            id: 'artificialIntelligence',
            label: 'Artificial Intelligence',
        },
        {
            id: 'quantumComputing',
            label: 'Quantum Computing',
        },
        {
            id: 'internetOfThings',
            label: 'Internet of Things (IoT)',
        },
        {
            id: '5gTechnology',
            label: '5G Technology',
        },
        {
            id: 'blockchainTechnology',
            label: 'Block Chain Technology',
        },
    ],
}

export const articleTagsData = {
    tags: [
        { id: 1, label: 'Configuring' },
        { id: 4, label: 'Customization' },
        { id: 5, label: 'Setup' },
    ],
}

class ArticleList {
    list = articleListData

    getList() {
        return this.list
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setList(list: any) {
        this.list = list
    }
}

export const articleList = new ArticleList()
