@keyframes scroll {
    to {
    transform: translateY(calc(-50% - 0.5rem));
    }
}

@keyframes aurora {
    from {
        background-position: 50% 50%, 50% 50%;
    }
    to {
        background-position: 350% 50%, 350% 50%;
    }
}

.scroll-animation {
    animation: scroll var(--animation-duration, 40s) var(--animation-direction, forwards) linear infinite;
}

.aurora-animation {
    animation: aurora 60s linear infinite;
}