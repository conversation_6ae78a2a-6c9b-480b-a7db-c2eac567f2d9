'use client'

import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'

interface ScheduleProps {
    data: Array<{
        start: Date
        end: Date
        name: string
        id: string
        progress: number
        type: string
        hideChildren?: boolean
        displayOrder: number
        barVariant: string
        project?: string
        dependencies?: string[]
    }>
}

const Schedule = ({ data }: ScheduleProps) => {
    const getBarColor = (barVariant: string) => {
        switch (barVariant) {
            case 'overallDesign':
                return 'bg-blue-500'
            case 'design':
                return 'bg-blue-300'
            case 'overallDevelopment':
                return 'bg-emerald-500'
            case 'development':
                return 'bg-emerald-300'
            default:
                return 'bg-gray-500'
        }
    }

    const getTypeIcon = (type: string) => {
        return type === 'project' ? '📁' : '📋'
    }

    const formatDate = (date: Date) => {
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        })
    }

    const getDuration = (start: Date, end: Date) => {
        const diffTime = Math.abs(end.getTime() - start.getTime())
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        return `${diffDays} days`
    }

    // Filter to show only main projects and some key tasks
    const filteredData = data?.filter(item => 
        item.type === 'project' || 
        (item.type === 'task' && item.progress > 0)
    ).slice(0, 6) || []

    return (
        <Card>
            <h5 className="mb-6">Project Schedule</h5>
            <div className="space-y-4">
                {filteredData.map((item) => (
                    <div key={item.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <span className="text-lg">{getTypeIcon(item.type)}</span>
                                <div>
                                    <div className={`font-medium ${item.type === 'project' ? 'text-base' : 'text-sm'}`}>
                                        {item.name}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                        {formatDate(item.start)} - {formatDate(item.end)} • {getDuration(item.start, item.end)}
                                    </div>
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="text-sm font-medium">{item.progress}%</div>
                            </div>
                        </div>
                        
                        <div className="ml-6">
                            <Progress
                                percent={item.progress}
                                showInfo={false}
                                size="sm"
                                customColorClass={getBarColor(item.barVariant)}
                            />
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Overall Progress</span>
                    <button className="text-sm text-primary hover:text-primary-dark font-medium">
                        View Gantt Chart
                    </button>
                </div>
            </div>
        </Card>
    )
}

export default Schedule
