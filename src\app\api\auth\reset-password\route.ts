import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import type { ResetPassword } from '@/@types/auth'

export async function POST(request: NextRequest) {
    try {
        const body: ResetPassword = await request.json()

        if (!body.password) {
            return NextResponse.json(
                { error: 'Password is required' },
                { status: 400 }
            )
        }

        const result = await authService.updatePassword(body.password)
        return NextResponse.json(result)
    } catch (error) {
        console.error('Reset password error:', error)
        const errorMessage = error instanceof Error ? error.message : 'An error occurred'
        return NextResponse.json({ error: errorMessage }, { status: 400 })
    }
}
