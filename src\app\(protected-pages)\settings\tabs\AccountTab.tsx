'use client'

import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { PiUserDuotone, PiEnvelopeDuotone } from 'react-icons/pi'

const AccountTab = () => {
    return (
        <div className="space-y-6">
            {/* Profile Information */}
            <Card>
                <h5 className="font-bold text-lg mb-4">Profile Information</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium mb-2">Full Name</label>
                        <Input 
                            placeholder="John Doe"
                            prefix={<PiUserDuotone />}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-2">Email Address</label>
                        <Input 
                            placeholder="<EMAIL>"
                            prefix={<PiEnvelopeDuotone />}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-2">Organization</label>
                        <Input placeholder="CheckGap Demo" />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-2">Role</label>
                        <Input placeholder="Compliance Manager" />
                    </div>
                </div>
                <div className="mt-4">
                    <Button variant="solid" className="bg-blue-500 hover:bg-blue-600">
                        Save Changes
                    </Button>
                </div>
            </Card>

            {/* Security Settings */}
            <Card>
                <h5 className="font-bold text-lg mb-4">Security Settings</h5>
                <div className="space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-gray-100 dark:border-gray-700">
                        <div>
                            <h6 className="font-medium">Two-Factor Authentication</h6>
                            <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
                        </div>
                        <Button size="sm" variant="default">Enable</Button>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-gray-100 dark:border-gray-700">
                        <div>
                            <h6 className="font-medium">Password</h6>
                            <p className="text-sm text-gray-500">Last changed 30 days ago</p>
                        </div>
                        <Button size="sm" variant="default">Change</Button>
                    </div>
                    <div className="flex justify-between items-center py-3">
                        <div>
                            <h6 className="font-medium">Login Sessions</h6>
                            <p className="text-sm text-gray-500">Manage your active sessions</p>
                        </div>
                        <Button size="sm" variant="default">View</Button>
                    </div>
                </div>
            </Card>

            {/* Preferences */}
            <Card>
                <h5 className="font-bold text-lg mb-4">Preferences</h5>
                <div className="space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-gray-100 dark:border-gray-700">
                        <div>
                            <h6 className="font-medium">Email Notifications</h6>
                            <p className="text-sm text-gray-500">Receive updates about compliance activities</p>
                        </div>
                        <Button size="sm" variant="default">Configure</Button>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-gray-100 dark:border-gray-700">
                        <div>
                            <h6 className="font-medium">Theme</h6>
                            <p className="text-sm text-gray-500">Choose your preferred appearance</p>
                        </div>
                        <Button size="sm" variant="default">Light</Button>
                    </div>
                    <div className="flex justify-between items-center py-3">
                        <div>
                            <h6 className="font-medium">Language</h6>
                            <p className="text-sm text-gray-500">Select your preferred language</p>
                        </div>
                        <Button size="sm" variant="default">English</Button>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default AccountTab
