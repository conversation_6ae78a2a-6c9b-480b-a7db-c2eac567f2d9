export const ordersData = [
    {
        id: '95954',
        date: 1660132800,
        customer: '<PERSON>',
        status: 0,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 6165',
        totalAmount: 168,
    },
    {
        id: '95423',
        date: 1659132800,
        customer: '<PERSON>',
        status: 0,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 7128',
        totalAmount: 523,
    },
    {
        id: '92903',
        date: 1658132800,
        customer: '<PERSON><PERSON>',
        status: 0,
        paymentMehod: 'paypal',
        paymentIdendifier: '••••@gmail.com',
        totalAmount: 81,
    },
    {
        id: '92627',
        date: 1657332800,
        customer: '<PERSON>',
        status: 0,
        paymentMehod: 'master',
        paymentIdendifier: '•••• 0921',
        totalAmount: 279,
    },
    {
        id: '92509',
        date: 1656232800,
        customer: '<PERSON>',
        status: 1,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 1232',
        totalAmount: 831,
    },
    {
        id: '91631',
        date: 1655532800,
        customer: '<PERSON> Hale',
        status: 0,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 4597',
        totalAmount: 142,
    },
    {
        id: '90963',
        date: 1654932800,
        customer: 'Luke Cook',
        status: 0,
        paymentMehod: 'master',
        paymentIdendifier: '•••• 3881',
        totalAmount: 232,
    },
    {
        id: '89332',
        date: 1654132800,
        customer: 'Eileen Horton',
        status: 1,
        paymentMehod: 'paypal',
        paymentIdendifier: '••••@gmail.com',
        totalAmount: 597,
    },
    {
        id: '89107',
        date: 1650132800,
        customer: 'Frederick Adams',
        status: 2,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 3356',
        totalAmount: 72,
    },
    {
        id: '89021',
        date: 1649832800,
        customer: 'Lee Wheeler',
        status: 0,
        paymentMehod: 'master',
        paymentIdendifier: '•••• 9564',
        totalAmount: 110,
    },
    {
        id: '88911',
        date: 1649432800,
        customer: 'Gail Barnes',
        status: 0,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 1357',
        totalAmount: 59,
    },
    {
        id: '87054',
        date: 1647932800,
        customer: 'Ella Robinson',
        status: 0,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 3561',
        totalAmount: 238,
    },
    {
        id: '86497',
        date: 1647632800,
        customer: 'Steve Sutton',
        status: 2,
        paymentMehod: 'visa',
        paymentIdendifier: '•••• 0443',
        totalAmount: 189,
    },
    {
        id: '86212',
        date: 1646832800,
        customer: 'Tara Fletcher',
        status: 0,
        paymentMehod: 'paypal',
        paymentIdendifier: '••••@gmail.com',
        totalAmount: 672,
    },
]

export const orderDetailsData = {
    id: '95954',
    progressStatus: 0,
    paymentStatus: 0,
    dateTime: 1646396117,
    paymentSummary: {
        subTotal: 1762,
        tax: 105.72,
        deliveryFees: 15,
        total: 1870.72,
        customerPayment: 1870.72,
    },
    shipping: {
        deliveryFees: 15,
        estimatedMin: 1,
        estimatedMax: 3,
        shippingLogo: '/img/others/img-11.jpg',
        shippingVendor: 'FedEx',
    },
    note: `If there are any issues or delays with my order, please don't hesitate to contact me, I value clear communication and appreciate your attention to detail.`,
    activity: [
        {
            date: 1646554397,
            events: [
                {
                    time: 1646554397,
                    action: 'Parcel has been delivered',
                    recipient: 'Steve Sutton',
                },
                {
                    time: 1646537537,
                    action: 'Parcel is out for delivery',
                },
                {
                    time: 1646529317,
                    action: 'Parcel has arrived at delivery station',
                },
            ],
        },
        {
            date: 1646442017,
            events: [
                {
                    time: 1646462597,
                    action: 'Parcel has been picked up by courier',
                },
                {
                    time: 1646537537,
                    action: 'Seller is preparing to ship your parcel',
                },
            ],
        },
    ],
    product: [
        {
            id: '13',
            name: 'Snövalla',
            productCode: '098359NT',
            img: '/img/products/product-2.jpg',
            price: 252,
            quantity: 2,
            total: 504,
        },
        {
            id: '18',
            name: 'Maneki Neko Poster',
            productCode: '098336NT',
            img: '/img/products/product-7.jpg',
            price: 389,
            quantity: 1,
            total: 389,
        },
        {
            id: '19',
            name: 'Ektöra',
            productCode: '098368NT',
            img: '/img/products/product-8.jpg',
            price: 869,
            quantity: 1,
            total: 869,
        },
    ],
    customer: {
        name: 'Steve Sutton',
        firstName: 'Steve',
        lastName: 'Sutton',
        email: '<EMAIL>',
        phone: '+****************',
        dialCode: '+1',
        phoneNumber: '************',
        img: '/img/avatars/thumb-11.jpg',
        previousOrder: 11,
        address: '123 Main St',
        postcode: '10001',
        city: 'New York',
        country: 'US',
        shippingAddress: {
            line1: '100 Main ST',
            line2: 'PO Box 1022',
            line3: 'Seattle WA 98104',
            line4: 'United States of America',
        },
        billingAddress: {
            line1: '1527 Pond Reef Rd',
            line2: 'Ketchikan',
            line3: 'Alaska 99901',
            line4: 'United States of America',
        },
    },
}
