'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'
import Segment from '@/components/ui/Segment'

interface TaskOverviewProps {
    data: {
        weekly: {
            onGoing: number
            finished: number
            total: number
            series: Array<{
                name: string
                data: number[]
            }>
            range: string[]
        }
        daily: {
            onGoing: number
            finished: number
            total: number
            series: Array<{
                name: string
                data: number[]
            }>
            range: string[]
        }
    }
}

const TaskOverview = ({ data }: TaskOverviewProps) => {
    const [selectedPeriod, setSelectedPeriod] = useState('weekly')

    const periodOptions = [
        { label: 'Weekly', value: 'weekly' },
        { label: 'Daily', value: 'daily' }
    ]

    const currentData = data?.[selectedPeriod as keyof typeof data] || {
        onGoing: 0,
        finished: 0,
        total: 0,
        series: [],
        range: []
    }

    const completionRate = currentData.total > 0 
        ? Math.round((currentData.finished / currentData.total) * 100) 
        : 0

    return (
        <Card>
            <div className="flex items-center justify-between mb-6">
                <h5>Task Overview</h5>
                <Segment
                    value={selectedPeriod}
                    onChange={(value) => setSelectedPeriod(value as string)}
                    size="sm"
                >
                    {periodOptions.map(option => (
                        <Segment.Item key={option.value} value={option.value}>
                            {option.label}
                        </Segment.Item>
                    ))}
                </Segment>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                    <div className="text-xl font-bold text-blue-600">
                        {currentData.onGoing}
                    </div>
                    <div className="text-sm text-gray-500">On Going</div>
                </div>
                <div className="text-center">
                    <div className="text-xl font-bold text-emerald-600">
                        {currentData.finished}
                    </div>
                    <div className="text-sm text-gray-500">Finished</div>
                </div>
                <div className="text-center">
                    <div className="text-xl font-bold text-gray-600">
                        {currentData.total}
                    </div>
                    <div className="text-sm text-gray-500">Total</div>
                </div>
            </div>

            <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-500">Completion Rate</span>
                    <span className="font-semibold">{completionRate}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                        className="bg-emerald-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${completionRate}%` }}
                    ></div>
                </div>
            </div>

            <Chart
                type="line"
                height={250}
                series={currentData.series}
                xAxis={currentData.range}
                customOptions={{
                    colors: ['#3B82F6', '#10B981'],
                    stroke: {
                        width: 3,
                        curve: 'smooth'
                    },
                    markers: {
                        size: 4
                    },
                    grid: {
                        borderColor: '#e5e7eb',
                        strokeDashArray: 5
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right'
                    }
                }}
            />
        </Card>
    )
}

export default TaskOverview
