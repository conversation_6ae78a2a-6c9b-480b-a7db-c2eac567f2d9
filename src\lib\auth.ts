import { supabase, isSupabaseConfigured } from './supabase'
import type { SignUpCredential, SignInCredential } from '@/@types/auth'

// Client-side authentication functions
export const authService = {
  // Sign up with email and password
  async signUp(credentials: SignUpCredential) {
    if (!isSupabaseConfigured()) {
      throw new Error('Authentication is not configured. Please contact support.')
    }

    const { data, error } = await supabase.auth.signUp({
      email: credentials.email,
      password: credentials.password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    })

    if (error) {
      throw new Error(error.message)
    }

    return {
      user: data.user,
      session: data.session,
      message: data.user?.email_confirmed_at 
        ? 'Account created successfully!' 
        : 'Please check your email to verify your account.'
    }
  },

  // Sign in with email and password
  async signIn(credentials: SignInCredential) {
    if (!isSupabaseConfigured()) {
      throw new Error('Authentication is not configured. Please contact support.')
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password
    })

    if (error) {
      throw new Error(error.message)
    }

    return {
      user: data.user,
      session: data.session
    }
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) {
      throw new Error(error.message)
    }
  },

  // Reset password
  async resetPassword(email: string) {
    if (!isSupabaseConfigured()) {
      throw new Error('Authentication is not configured. Please contact support.')
    }

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    })

    if (error) {
      throw new Error(error.message)
    }

    return { message: 'Password reset email sent!' }
  },

  // Update password (for reset password flow)
  async updatePassword(newPassword: string) {
    if (!isSupabaseConfigured()) {
      throw new Error('Authentication is not configured. Please contact support.')
    }

    const { error } = await supabase.auth.updateUser({
      password: newPassword
    })

    if (error) {
      throw new Error(error.message)
    }

    return { message: 'Password updated successfully!' }
  },

  // Get current session
  async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession()
    if (error) {
      throw new Error(error.message)
    }
    return session
  },

  // Get current user
  async getUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) {
      throw new Error(error.message)
    }
    return user
  }
}

// Auth state change listener
export const onAuthStateChange = (callback: (event: string, session: unknown) => void) => {
  return supabase.auth.onAuthStateChange(callback)
}
