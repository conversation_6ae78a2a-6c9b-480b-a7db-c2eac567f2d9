'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'
import Segment from '@/components/ui/Segment'
import { TbTrendingUp, TbTrendingDown, TbShoppingCart, TbBuildingStore, TbBrandInstagram } from 'react-icons/tb'

interface RevenueByChannelProps {
    data: {
        thisWeek: {
            value: number
            growShrink: number
            percentage: {
                onlineStore: number
                physicalStore: number
                socialMedia: number
            }
        }
        thisMonth: {
            value: number
            growShrink: number
            percentage: {
                onlineStore: number
                physicalStore: number
                socialMedia: number
            }
        }
        thisYear: {
            value: number
            growShrink: number
            percentage: {
                onlineStore: number
                physicalStore: number
                socialMedia: number
            }
        }
    }
}

const RevenueByChannel = ({ data }: RevenueByChannelProps) => {
    const [selectedPeriod, setSelectedPeriod] = useState('thisWeek')

    const periodOptions = [
        { label: 'Week', value: 'thisWeek' },
        { label: 'Month', value: 'thisMonth' },
        { label: 'Year', value: 'thisYear' }
    ]

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value)
    }

    const renderGrowthIndicator = (growShrink: number) => {
        const isPositive = growShrink > 0
        return (
            <div className={`flex items-center gap-1 ${isPositive ? 'text-emerald-500' : 'text-red-500'}`}>
                {isPositive ? <TbTrendingUp className="w-4 h-4" /> : <TbTrendingDown className="w-4 h-4" />}
                <span className="text-sm font-medium">{Math.abs(growShrink)}%</span>
            </div>
        )
    }

    const currentData = data?.[selectedPeriod as keyof typeof data] || {
        value: 0,
        growShrink: 0,
        percentage: { onlineStore: 0, physicalStore: 0, socialMedia: 0 }
    }

    const channels = [
        {
            name: 'Online Store',
            icon: <TbShoppingCart className="w-5 h-5" />,
            percentage: currentData.percentage.onlineStore,
            color: 'bg-blue-500'
        },
        {
            name: 'Physical Store',
            icon: <TbBuildingStore className="w-5 h-5" />,
            percentage: currentData.percentage.physicalStore,
            color: 'bg-emerald-500'
        },
        {
            name: 'Social Media',
            icon: <TbBrandInstagram className="w-5 h-5" />,
            percentage: currentData.percentage.socialMedia,
            color: 'bg-pink-500'
        }
    ]

    const chartData = channels.map(channel => channel.percentage)
    const chartLabels = channels.map(channel => channel.name)
    const chartColors = channels.map(channel => {
        switch (channel.color) {
            case 'bg-blue-500': return '#3B82F6'
            case 'bg-emerald-500': return '#10B981'
            case 'bg-pink-500': return '#EC4899'
            default: return '#6B7280'
        }
    })

    return (
        <Card>
            <div className="flex items-center justify-between mb-6">
                <h5>Revenue by Channel</h5>
                <Segment
                    value={selectedPeriod}
                    onChange={(value) => setSelectedPeriod(value as string)}
                    size="sm"
                >
                    {periodOptions.map(option => (
                        <Segment.Item key={option.value} value={option.value}>
                            {option.label}
                        </Segment.Item>
                    ))}
                </Segment>
            </div>

            <div className="text-center mb-4">
                <div className="text-2xl font-bold">
                    {formatCurrency(currentData.value)}
                </div>
                <div className="flex items-center justify-center gap-2 mt-1">
                    {renderGrowthIndicator(currentData.growShrink)}
                    <span className="text-sm text-gray-500">vs last period</span>
                </div>
            </div>

            <div className="mb-6">
                <Chart
                    type="donut"
                    height={200}
                    series={chartData}
                    customOptions={{
                        labels: chartLabels,
                        colors: chartColors,
                        legend: { show: false },
                        plotOptions: {
                            pie: {
                                donut: {
                                    size: '70%'
                                }
                            }
                        }
                    }}
                />
            </div>

            <div className="space-y-3">
                {channels.map((channel) => (
                    <div key={channel.name} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg ${channel.color} text-white`}>
                                {channel.icon}
                            </div>
                            <span className="font-medium text-sm">{channel.name}</span>
                        </div>
                        <span className="font-semibold">{channel.percentage}%</span>
                    </div>
                ))}
            </div>
        </Card>
    )
}

export default RevenueByChannel
