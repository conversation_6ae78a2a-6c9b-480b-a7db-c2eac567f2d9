export const chatHistoryData = [
    {
        id: '1',
        title: 'Camera outfit',
        lastConversation:
            'I have a photoshoot tomorrow. Can you recommend me some colors and outfit options that will look good on camera?',
        conversation: [
            {
                id: 'b0474144-2815-44c0-bbaf-165edffce238',
                sender: {
                    id: '1',
                    name: '<PERSON>',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content:
                    'I have a photoshoot tomorrow. Can you recommend me some colors and outfit options that will look good on camera?',
                timestamp: **********,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'efc25c82-d429-4816-ac46-e6b8760bdbad',
                sender: {
                    id: 'ai',
                    name: '<PERSON><PERSON> <PERSON>',
                    avatarImageUrl: '/img/thumbs/ai.jpg',
                },
                content:
                    'Sure! Here are some color and outfit suggestions that typically look great in photoshoots:\n\n1. Jewel tones such as emerald green, royal blue, and ruby red always photograph well and can make you stand out in a photo.\n\n2. Neutral tones like black, white, gray, and nude can create a classic and timeless look in photos.\n\n3. Patterns like stripes, polka dots, or florals can add visual interest to your outfit without overpowering the image.\n\n4. Avoid wearing busy patterns or logos as they can be distracting in photos.\n\n5. Consider wearing form-fitting clothing that flatters your body shape, but also allows you to move comfortably during the shoot.\n\n6. Layering different textures and fabrics can add depth and dimension to your outfit in photos.\n\nRemember to choose colors and outfits that make you feel confident and comfortable, as that will ultimately translate well on camera. Good luck with your photoshoot!',
                timestamp: **********,
                type: 'regular',
                isMyMessage: false,
            },
        ],
        createdTime: **********,
        updatedTime: **********,
        enable: true,
    },
    {
        id: '2',
        title: 'Vacation planning',
        lastConversation:
            "I'm planning a trip to Europe next summer. Can you help me with a travel itinerary?",
        conversation: [
            {
                id: 'ad00e323-eefb-4803-b2ca-ba0bdb0116e1',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `I'm planning a trip to Europe next summer. Can you help me with a travel itinerary?`,
                timestamp: **********,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'b4b90ebb-b23b-4fbe-bfda-927c04a37101',
                sender: {
                    id: 'ai',
                    name: 'Chat AI',
                    avatarImageUrl: '/img/thumbs/ai.jpg',
                },
                content:
                    'Of course! Europe is a vast and diverse continent with so much to offer. To create a personalized itinerary for you, I would need some more information. \n\n- How long will your trip be?\n- Which countries or cities are you most interested in visiting?\n- What are your interests and preferences (e.g. history, food, outdoor activities)?\n- What is your budget for the trip?\n\nOnce I have this information, I can suggest a detailed itinerary for you that includes transportation, accommodation, activities, and must-see sights. Let me know your preferences so I can help you plan a memorable trip to Europe!',
                timestamp: **********,
                type: 'regular',
                isMyMessage: false,
            },
            {
                id: 'c9f90ed9-7e2b-4519-b707-617ad140e87a',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Its about 2 weeks`,
                timestamp: **********,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'e7f7b6c2-a8b9-45a4-90e1-55207fc3db3d',
                sender: {
                    id: 'ai',
                    name: 'Chat AI',
                    avatarImageUrl: '/img/thumbs/ai.jpg',
                },
                content: `Here is a sample 2-week itinerary for a trip to Europe:\n\nDay 1-3: London, England\n- Visit iconic landmarks such as Big Ben, Buckingham Palace, and the Tower of London\n- Explore the British Museum and take a ride on the London Eye\n- Enjoy a traditional English afternoon tea\n\nDay 4-6: Paris, France\n- Visit the Eiffel Tower, Louvre Museum, and Notre Dame Cathedral\n- Stroll along the Seine River and explore Montmartre\n- Enjoy a croissant at a local Parisian café\n\nDay 7-9: Rome, Italy\n- Visit the Colosseum, Roman Forum, and Vatican City\n- Explore the historic neighborhoods of Trastevere and Testaccio\n- Indulge in authentic Italian pasta and gelato\n\nDay 10-12: Barcelona, Spain\n- Visit Gaudi's famous landmarks including Sagrada Familia and Park Guell\n- Explore the Gothic Quarter and relax on the sandy beaches\n- Taste tapas and sip sangria at a local bodega\n\nDay 13-14: Amsterdam, Netherlands\n- Explore the historic canals and visit the Anne Frank House\n- Visit the Van Gogh Museum and take a bike ride around the city\n- Enjoy a canal cruise and try some traditional Dutch stroopwafels\n\nThis itinerary covers some of the most popular cities in Europe and gives you a taste of each country's culture. Feel free to customize it to better suit your interests and travel style. Have a fantastic trip!`,
                timestamp: **********,
                type: 'regular',
                isMyMessage: false,
            },
        ],
        createdTime: **********,
        updatedTime: **********,
        enable: true,
    },
    {
        id: '3',
        title: 'Healthy recipes',
        lastConversation:
            'Can you suggest some healthy dinner recipes that are quick to prepare?',
        conversation: [
            {
                id: 'ba482dfa-dff4-4f6b-ab16-a84b8eee47c4',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Can you suggest some healthy dinner recipes that are quick to prepare?`,
                timestamp: **********,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'cbb07e7d-af72-4b5b-8023-bdd0b43bf233',
                sender: {
                    id: 'ai',
                    name: 'Chat AI',
                    avatarImageUrl: '/img/thumbs/ai.jpg',
                },
                content:
                    '1. Grilled salmon with lemon and herbs: Marinate salmon fillets in olive oil, lemon juice, garlic, and herbs, then grill until cooked through. Serve with a side of roasted vegetables or a mixed green salad.\n\n2. Turkey and vegetable stir-fry: Sauté ground turkey with colorful vegetables like bell peppers, broccoli, and snow peas in a stir-fry sauce. Serve over brown rice or quinoa for a balanced meal.\n\n3. Chickpea and spinach curry: Cook chickpeas in a flavorful tomato and coconut milk curry sauce with baby spinach. Serve with naan bread or brown rice for a satisfying vegetarian meal.\n\n4. Zucchini noodles with pesto and cherry tomatoes: Make zucchini noodles using a spiralizer, then toss with homemade or store-bought pesto sauce and halved cherry tomatoes. Top with grilled chicken or shrimp for added protein.\n\n5. Caprese grilled chicken: Top grilled chicken breasts with fresh mozzarella, sliced tomatoes, and basil leaves. Drizzle with balsamic glaze and serve with a side of quinoa or a mixed greens salad.\n\nThese recipes are not only quick to prepare but also nutritious and delicious. Enjoy your healthy dinner!',
                timestamp: **********,
                type: 'regular',
                isMyMessage: false,
            },
        ],
        createdTime: **********,
        updatedTime: **********,
        enable: true,
    },
    {
        id: '4',
        title: 'Exercise routine',
        lastConversation:
            "I'm looking to start a new exercise routine. What are some effective workouts for beginners?",
        conversation: [
            {
                id: '03a38bfa-1e64-4e40-a1a0-4a5c24be6851',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `I'm looking to start a new exercise routine. What are some effective workouts for beginners?`,
                timestamp: **********,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'aa1ce056-4754-4be9-bbde-4e85c2bc3166',
                sender: {
                    id: 'ai',
                    name: 'Chat AI',
                    avatarImageUrl: '/img/thumbs/ai.jpg',
                },
                content: `Here are some effective workouts for beginners to consider:\n\n1. Walking or jogging: Start with a brisk walk and gradually build up to jogging or running.\n\n2. Bodyweight exercises: Include exercises such as squats, lunges, push-ups, and planks to build strength and improve overall fitness.\n\n3. Yoga: Try out a beginner's yoga class to improve flexibility, balance, and core strength.\n\n4. Swimming: Swimming is a great low-impact workout that can help improve cardiovascular fitness and muscle tone.\n\n5. Cycling: Whether you're cycling outdoors or using a stationary bike, cycling is a great way to get your heart rate up and build endurance.\n\n6. Group fitness classes: Consider taking a beginner-friendly fitness class such as Zumba, Pilates, or circuit training to mix up your routine and stay motivated.\n\nRemember to start slowly, listen to your body, and consult with a fitness professional if you have any concerns or medical conditions. Good luck with your new exercise routine!`,
                timestamp: **********,
                type: 'regular',
                isMyMessage: false,
            },
        ],
        createdTime: **********,
        updatedTime: **********,
        enable: true,
    },
    {
        id: '5',
        title: 'Max number Python function',
        lastConversation:
            'Write a Python function that finding the maximum number in a list',
        conversation: [
            {
                id: '0dd185c4-63e4-4db6-8162-859efe3018dc',
                sender: {
                    id: '1',
                    name: 'Angelina Gotelli',
                    avatarImageUrl: '/img/avatars/thumb-1.jpg',
                },
                content: `Write a Python function that finding the maximum number in a list`,
                timestamp: **********,
                type: 'regular',
                isMyMessage: true,
            },
            {
                id: 'a163977a-3fa5-4b68-824e-72d0ff06eedd',
                sender: {
                    id: 'ai',
                    name: 'Chat AI',
                    avatarImageUrl: '/img/thumbs/ai.jpg',
                },
                content:
                    'Sure, here is an example code in Python for finding the maximum number in a list:\n\n```python\ndef find_max_number(nums):\n    if not nums:\n        return None\n      \n    max_num = nums[0]\n    for num in nums:\n        if num > max_num:\n            max_num = num\n\n    return max_num\n\n# Test cases\nprint(find_max_number([1, 5, 10, 3, 8]))  # Output should be 10\nprint(find_max_number([-1, -5, -3, -8]))  # Output should be -1\nprint(find_max_number([]))  # Output should be None\n```\n\nThis code defines a function `find_max_number` that takes a list of numbers as input and returns the maximum number in the list. It handles the edge case of an empty list by returning `None`.',
                timestamp: **********,
                type: 'regular',
                isMyMessage: false,
            },
        ],
        createdTime: 1723786000,
        updatedTime: 1723786000,
        enable: true,
    },
]

export const imageData = [
    {
        id: '1',
        prompt: 'While picnicking, you will see a beautiful image, a view of the sky, with shades of blue and fuchsia, observing the planets and stars.',
        image: '/img/others/gallery/img-1.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 5,
    },
    {
        id: '2',
        prompt: 'Dark Text that says "Team LAL" with various cats and dogs in cartoon style in the background. Image background should be completely white ',
        image: '/img/others/gallery/img-2.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '3',
        prompt: 'cat smoking while fishing in the woods',
        image: '/img/others/gallery/img-3.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '4',
        prompt: 'A man observer who sketches breath life into memory',
        image: '/img/others/gallery/img-4.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '5',
        prompt: 'create a broken jar in the style of this image and keep the african wax batik style and dip dye with crack effects.',
        image: '/img/others/gallery/img-5.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '6',
        prompt: 'light saber spiderman and batman',
        image: '/img/others/gallery/img-6.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '7',
        prompt: 'make me a logo that will have a pink cat in it that will have a text "C2" below it',
        image: '/img/others/gallery/img-7.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '8',
        prompt: 'A gray cat in a suit and a kitten in a brown suit next to her',
        image: '/img/others/gallery/img-8.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '9',
        prompt: 'Create a realistic image of a 30 year old blonde women and a 30 year old man with black hair that are jumping from joy',
        image: '/img/others/gallery/img-9.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '10',
        prompt: 'a pair of pink polymer clay earrings set up as a flat lay image',
        image: '/img/others/gallery/img-10.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '11',
        prompt: 'Zodiac Sign Sagittarius: An Golden Retriever with sunglasses, shirt, cowboy-hat, in the Australia Sydney, exploring the desert at high noon. GTA loading screen style (warm tone) (golden hour)',
        image: '/img/others/gallery/img-11.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '12',
        prompt: 'A young woman sits cross-legged on her sofa. She plays the guitar gently. Colors and shapes escape.',
        image: '/img/others/gallery/img-12.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '13',
        prompt: 'Best pose',
        image: '/img/others/gallery/img-13.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '14',
        prompt: 'FOOTLOOKER MONSTER',
        image: '/img/others/gallery/img-14.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '15',
        prompt: 'Dug well A to Z (cool tone) (golden hour)',
        image: '/img/others/gallery/img-15.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '16',
        prompt: 'generate me images for the theme of the birthday of a person named Fany and therefore the birthday is in June!!',
        image: '/img/others/gallery/img-16.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '17',
        prompt: 'beach',
        image: '/img/others/gallery/img-17.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '18',
        prompt: `A faucet (slot machine), inside of which there are spheres inscribed with runes. In the background are Marvel characters`,
        image: '/img/others/gallery/img-18.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '19',
        prompt: 'cozy Nipa Hut the beach',
        image: '/img/others/gallery/img-19.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '20',
        prompt: `It side images`,
        image: '/img/others/gallery/img-20.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '21',
        prompt: `(all full black yorkie) with a (black and white cat) both in the air on a kite (sunlight)`,
        image: '/img/others/gallery/img-21.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '22',
        prompt: `GitHub avatar for a programmer (pastel colors) (studio)`,
        image: '/img/others/gallery/img-22.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '23',
        prompt: `PhD (Permanent Head Damage) (vibrant colors) (studio) (close up)`,
        image: '/img/others/gallery/img-23.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '24',
        prompt: `A photo of a dragon that looks injured after a tough battel with hole in his wings and fire and smoke come out his injuries`,
        image: '/img/others/gallery/img-24.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '25',
        prompt: `make me a cereal box with the title "Sugar Cloud Crunch"`,
        image: '/img/others/gallery/img-25.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '26',
        prompt: `a fox wearing red hoodie and black headphones, neon red theme and black background`,
        image: '/img/others/gallery/img-26.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '27',
        prompt: `make a poster image with a light blue dominant and a light brown secondary color`,
        image: '/img/others/gallery/img-27.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '28',
        prompt: `Cute rabbit wearing a jacket, eating a carrot, 3D Style, rendering`,
        image: '/img/others/gallery/img-28.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '29',
        prompt: `a humanoid wolf dressed in a purple hoodie in headphones holding a gaming controller (backlight)`,
        image: '/img/others/gallery/img-29.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '30',
        prompt: `1930'S pulp art comic book cover theme: GODZILLA TALKS!! (show in comic dialogue bubble) (warm tone) (dramatic) (wide angle)`,
        image: '/img/others/gallery/img-30.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '31',
        prompt: `pink convers`,
        image: '/img/others/gallery/img-31.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '32',
        prompt: `Anime boys`,
        image: '/img/others/gallery/img-32.webp',
        ratio: '4:7',
        size: '768 x 1344 px',
        like: 0,
    },
    {
        id: '33',
        prompt: `hello kitty and a gorilla`,
        image: '/img/others/gallery/img-33.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '34',
        prompt: `trees`,
        image: '/img/others/gallery/img-34.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '35',
        prompt: `japanese wave`,
        image: '/img/others/gallery/img-35.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
    {
        id: '36',
        prompt: `A panda falling love`,
        image: '/img/others/gallery/img-36.webp',
        ratio: '1:1',
        size: '1024 x 1024 px',
        like: 0,
    },
]

export const generatedImageData = [
    [
        {
            id: '1',
            prompt: '',
            image: '/img/others/gallery/generated/img-1-1.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '2',
            prompt: '',
            image: '/img/others/gallery/generated/img-1-2.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '3',
            prompt: '',
            image: '/img/others/gallery/generated/img-1-3.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '4',
            prompt: '',
            image: '/img/others/gallery/generated/img-1-4.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
    ],
    [
        {
            id: '1',
            prompt: '',
            image: '/img/others/gallery/generated/img-2-1.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '2',
            prompt: '',
            image: '/img/others/gallery/generated/img-2-2.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '3',
            prompt: '',
            image: '/img/others/gallery/generated/img-2-3.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '4',
            prompt: '',
            image: '/img/others/gallery/generated/img-2-4.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
    ],
    [
        {
            id: '1',
            prompt: '',
            image: '/img/others/gallery/generated/img-3-1.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '2',
            prompt: '',
            image: '/img/others/gallery/generated/img-3-2.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '3',
            prompt: '',
            image: '/img/others/gallery/generated/img-3-3.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '4',
            prompt: '',
            image: '/img/others/gallery/generated/img-3-4.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
    ],
    [
        {
            id: '1',
            prompt: '',
            image: '/img/others/gallery/generated/img-4-1.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '2',
            prompt: '',
            image: '/img/others/gallery/generated/img-4-2.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '3',
            prompt: '',
            image: '/img/others/gallery/generated/img-4-3.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '4',
            prompt: '',
            image: '/img/others/gallery/generated/img-4-4.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
    ],
    [
        {
            id: '1',
            prompt: '',
            image: '/img/others/gallery/generated/img-5-1.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '2',
            prompt: '',
            image: '/img/others/gallery/generated/img-5-2.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '3',
            prompt: '',
            image: '/img/others/gallery/generated/img-5-3.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
        {
            id: '4',
            prompt: '',
            image: '/img/others/gallery/generated/img-5-4.webp',
            ratio: '1:1',
            size: '1024 x 1024 px',
            like: 0,
        },
    ],
]
