export const fileListData = [
    {
        id: '1',
        name: 'Tech design.pdf',
        fileType: 'pdf',
        srcUrl: '',
        size: 2202009.6,
        author: {
            name: '<PERSON>',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-5.jpg',
        },
        activities: [
            {
                userName: '<PERSON>',
                userImg: '/img/avatars/thumb-10.jpg',
                actionType: 'EDIT',
                timestamp: 1710163777,
            },
            {
                userName: '<PERSON>',
                userImg: '/img/avatars/thumb-7.jpg',
                actionType: 'EDIT',
                timestamp: 1710163777,
            },
            {
                userName: '<PERSON>',
                userImg: '/img/avatars/thumb-5.jpg',
                actionType: 'CREATE',
                timestamp: 1710163777,
            },
        ],
        permissions: [
            {
                userName: '<PERSON>',
                userImg: '/img/avatars/thumb-10.jpg',
                role: 'editor',
            },
            {
                userName: '<PERSON>',
                userImg: '/img/avatars/thumb-7.jpg',
                role: 'editor',
            },
            {
                userName: '<PERSON>',
                userImg: '/img/avatars/thumb-5.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1710163777,
        recent: true,
    },
    {
        id: '2',
        name: 'Financial_Report.xlsx',
        fileType: 'xls',
        srcUrl: '',
        size: 1458904.3,
        author: {
            name: 'Jackie Soto',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-15.jpg',
        },
        activities: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                actionType: 'VIEW',
                timestamp: 1723045896,
            },
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                actionType: 'EDIT',
                timestamp: 1723045896,
            },
            {
                userName: 'Jackie Soto',
                userImg: '/img/avatars/thumb-15.jpg',
                actionType: 'DOWNLOAD',
                timestamp: 1723045896,
            },
        ],
        permissions: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                role: 'editor',
            },
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                role: 'editor',
            },
            {
                userName: 'Jackie Soto',
                userImg: '/img/avatars/thumb-15.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1723045896,
        recent: false,
    },
    {
        id: '3',
        name: 'Modern_Laputa.jpg',
        fileType: 'jpeg',
        srcUrl: '/img/others/img-3.jpg',
        size: 139234.8,
        author: {
            name: 'Camila Simmmons',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-9.jpg',
        },
        activities: [
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                actionType: 'VIEW',
                timestamp: 1721487234,
            },
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                actionType: 'CREATE',
                timestamp: 1721487234,
            },
        ],
        permissions: [
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                role: 'reader',
            },
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1721487234,
        recent: true,
    },
    {
        id: '4',
        name: 'Project_Presentation.pptx',
        fileType: 'ppt',
        srcUrl: '',
        size: 3056789.1,
        author: {
            name: 'Arlene Pierce',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-6.jpg',
        },
        activities: [
            {
                userName: 'Arlene Pierce',
                userImg: '/img/avatars/thumb-6.jpg',
                actionType: 'CREATE',
                timestamp: 1724509123,
            },
        ],
        permissions: [
            {
                userName: 'Arlene Pierce',
                userImg: '/img/avatars/thumb-6.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1724509123,
        recent: false,
    },
    {
        id: '5',
        name: 'Network_Diagram.fig',
        fileType: 'figma',
        srcUrl: '',
        size: 123456.7,
        author: {
            name: 'Camila Simmmons',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-9.jpg',
        },
        activities: [
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                actionType: 'CREATE',
                timestamp: 1726023412,
            },
        ],
        permissions: [
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1726023412,
        recent: true,
    },
    {
        id: '6',
        name: 'Project_Files',
        fileType: 'directory',
        srcUrl: '',
        size: 21797654.2,
        author: {
            name: 'Jessica Wells',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-8.jpg',
        },
        activities: [
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                actionType: 'CREATE',
                timestamp: 1740000000,
            },
        ],
        permissions: [
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1740000000,
        recent: true,
    },
    {
        id: '7',
        name: 'Project_Summary.docx',
        fileType: 'doc',
        srcUrl: '',
        size: 987654.2,
        author: {
            name: 'Steve Sutton',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-11.jpg',
        },
        activities: [
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                actionType: 'EDIT',
                timestamp: 1730156789,
            },
            {
                userName: 'Steve Sutton',
                userImg: '/img/avatars/thumb-11.jpg',
                actionType: 'CREATE',
                timestamp: 1730156789,
            },
        ],
        permissions: [
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                role: 'editor',
            },
            {
                userName: 'Steve Sutton',
                userImg: '/img/avatars/thumb-11.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1730156789,
        recent: false,
    },
    {
        id: '8',
        name: 'Gradient_store.jpg',
        fileType: 'jpeg',
        srcUrl: '/img/others/img-5.jpg',
        size: 157890.8,
        author: {
            name: 'Jackie Soto',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-15.jpg',
        },
        activities: [
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                actionType: 'VIEW',
                timestamp: 1732012345,
            },
            {
                userName: 'Jackie Soto',
                userImg: '/img/avatars/thumb-15.jpg',
                actionType: 'CREATE',
                timestamp: 1732012345,
            },
        ],
        permissions: [
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                role: 'reader',
            },
            {
                userName: 'Jackie Soto',
                userImg: '/img/avatars/thumb-15.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1732012345,
        recent: true,
    },
    {
        id: '9',
        name: 'Colorful_donunt.jpg',
        fileType: 'jpeg',
        srcUrl: '/img/others/img-6.jpg',
        size: 216789.5,
        author: {
            name: 'Angelina Gotelli',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-1.jpg',
        },
        activities: [
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                actionType: 'VIEW',
                timestamp: 1733890123,
            },
            {
                userName: 'Max Alexander',
                userImg: '/img/avatars/thumb-3.jpg',
                actionType: 'DOWNLOAD',
                timestamp: 1733890123,
            },
            {
                userName: 'Angelina Gotelli',
                userImg: '/img/avatars/thumb-11.jpg',
                actionType: 'CREATE',
                timestamp: 1733890123,
            },
        ],
        permissions: [
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                role: 'reader',
            },
            {
                userName: 'Max Alexander',
                userImg: '/img/avatars/thumb-3.jpg',
                role: 'reader',
            },
            {
                userName: 'Angelina Gotelli',
                userImg: '/img/avatars/thumb-11.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1733890123,
        recent: false,
    },
    {
        id: '10',
        name: 'Annual_Report.pdf',
        fileType: 'pdf',
        srcUrl: '',
        size: 1678901.4,
        author: {
            name: 'Eugene Stewart',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-5.jpg',
        },
        activities: [
            {
                userName: 'Jeremiah Minsk',
                userImg: '/img/avatars/thumb-2.jpg',
                actionType: 'VIEW',
                timestamp: 1735789012,
            },
            {
                userName: 'Eugene Stewart',
                userImg: '/img/avatars/thumb-5.jpg',
                actionType: 'CREATE',
                timestamp: 1735789012,
            },
        ],
        permissions: [
            {
                userName: 'Jeremiah Minsk',
                userImg: '/img/avatars/thumb-2.jpg',
                role: 'reader',
            },
            {
                userName: 'Eugene Stewart',
                userImg: '/img/avatars/thumb-5.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1735789012,
        recent: true,
    },
    {
        id: '11',
        name: 'Research_Paper.docx',
        fileType: 'doc',
        srcUrl: '',
        size: 987654.2,
        author: {
            name: 'Shannon Baker',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-4.jpg',
        },
        activities: [
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                actionType: 'VIEW',
                timestamp: 1737652345,
            },
            {
                userName: 'Angelina Gotelli',
                userImg: '/img/avatars/thumb-1.jpg',
                actionType: 'EDIT',
                timestamp: 1737652345,
            },
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                actionType: 'DOWNLOAD',
                timestamp: 1737652345,
            },
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                actionType: 'CREATE',
                timestamp: 1737652345,
            },
        ],
        permissions: [
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                role: 'reader',
            },
            {
                userName: 'Angelina Gotelli',
                userImg: '/img/avatars/thumb-1.jpg',
                role: 'editor',
            },
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                role: 'reader',
            },
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1737652345,
        recent: false,
    },
    {
        id: '12',
        name: 'Documents',
        fileType: 'directory',
        srcUrl: '',
        size: 10485760,
        author: {
            name: 'Earl Miles',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-10.jpg',
        },
        activities: [
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                actionType: 'CREATE',
                timestamp: 1740100000,
            },
        ],
        permissions: [
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1740100000,
        recent: true,
    },

    {
        id: '13',
        name: 'Budget_Report.pdf',
        fileType: 'pdf',
        srcUrl: '',
        size: 1678901.4,
        author: {
            name: 'Cassandra Murray',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-13.jpg',
        },
        activities: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                actionType: 'VIEW',
                timestamp: 1728123456,
            },
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                actionType: 'CREATE',
                timestamp: 1728123456,
            },
        ],
        permissions: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                role: 'editor',
            },
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1728123456,
        recent: true,
    },
    {
        id: '14',
        name: 'Marketing_Strategy.pptx',
        fileType: 'ppt',
        srcUrl: '',
        size: 2157890.8,
        author: {
            name: 'Jackie Soto',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-15.jpg',
        },
        activities: [
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                actionType: 'VIEW',
                timestamp: 1732012345,
            },
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                actionType: 'EDIT',
                timestamp: 1732012345,
            },
            {
                userName: 'Jackie Soto',
                userImg: '/img/avatars/thumb-15.jpg',
                actionType: 'CREATE',
                timestamp: 1732012345,
            },
        ],
        permissions: [
            {
                userName: 'Jessica Wells',
                userImg: '/img/avatars/thumb-8.jpg',
                role: 'reader',
            },
            {
                userName: 'Earl Miles',
                userImg: '/img/avatars/thumb-10.jpg',
                role: 'editor',
            },
            {
                userName: 'Jackie Soto',
                userImg: '/img/avatars/thumb-15.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1732012345,
        recent: true,
    },
    {
        id: '15',
        name: 'Architecture_Diagram.fig',
        fileType: 'figma',
        srcUrl: '',
        size: 456789.5,
        author: {
            name: 'Angelina Gotelli',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-1.jpg',
        },
        activities: [
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                actionType: 'VIEW',
                timestamp: 1733890123,
            },
            {
                userName: 'Roberta Horton',
                userImg: '/img/avatars/thumb-7.jpg',
                actionType: 'EDIT',
                timestamp: 1733890123,
            },
            {
                userName: 'Max Alexander',
                userImg: '/img/avatars/thumb-3.jpg',
                actionType: 'DOWNLOAD',
                timestamp: 1733890123,
            },
            {
                userName: 'Angelina Gotelli',
                userImg: '/img/avatars/thumb-11.jpg',
                actionType: 'CREATE',
                timestamp: 1733890123,
            },
        ],
        permissions: [
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                role: 'reader',
            },
            {
                userName: 'Roberta Horton',
                userImg: '/img/avatars/thumb-7.jpg',
                role: 'editor',
            },
            {
                userName: 'Max Alexander',
                userImg: '/img/avatars/thumb-3.jpg',
                role: 'editor',
            },
            {
                userName: 'Angelina Gotelli',
                userImg: '/img/avatars/thumb-11.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1733890123,
        recent: false,
    },
    {
        id: '16',
        name: 'Lone_bear.jpg',
        fileType: 'jpeg',
        srcUrl: '/img/others/img-4.jpg',
        size: 1678901.4,
        author: {
            name: 'Cassandra Murray',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-13.jpg',
        },
        activities: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                actionType: 'VIEW',
                timestamp: 1728123456,
            },
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                actionType: 'CREATE',
                timestamp: 1728123456,
            },
        ],
        permissions: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                role: 'reader',
            },
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1728123456,
        recent: true,
    },
    {
        id: '17',
        name: 'Meeting_Minutes.docx',
        fileType: 'doc',
        srcUrl: '',
        size: 789234.8,
        author: {
            name: 'Camila Simmmons',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-9.jpg',
        },
        activities: [
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                actionType: 'VIEW',
                timestamp: 1721487234,
            },
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                actionType: 'CREATE',
                timestamp: 1721487234,
            },
        ],
        permissions: [
            {
                userName: 'Cassandra Murray',
                userImg: '/img/avatars/thumb-13.jpg',
                role: 'editor',
            },
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1721487234,
        recent: true,
    },
    {
        id: '18',
        name: 'Team_Resources',
        fileType: 'directory',
        srcUrl: '',
        size: 783120,
        author: {
            name: 'Miriam Herrera',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-12.jpg',
        },
        activities: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                actionType: 'CREATE',
                timestamp: 1740200000,
            },
        ],
        permissions: [
            {
                userName: 'Miriam Herrera',
                userImg: '/img/avatars/thumb-12.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1740200000,
        recent: true,
    },
    {
        id: '19',
        name: 'Client_Data',
        fileType: 'directory',
        srcUrl: '',
        size: 5390720,
        author: {
            name: 'Camila Simmmons',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-9.jpg',
        },
        activities: [
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                actionType: 'CREATE',
                timestamp: 1740300000,
            },
        ],
        permissions: [
            {
                userName: 'Camila Simmmons',
                userImg: '/img/avatars/thumb-9.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1740300000,
        recent: true,
    },
    {
        id: '20',
        name: 'Backup_Files',
        fileType: 'directory',
        srcUrl: '',
        size: 2483948,
        author: {
            name: 'Shannon Baker',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-4.jpg',
        },
        activities: [
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                actionType: 'CREATE',
                timestamp: 1740400000,
            },
        ],
        permissions: [
            {
                userName: 'Shannon Baker',
                userImg: '/img/avatars/thumb-4.jpg',
                role: 'owner',
            },
        ],
        uploadDate: 1740400000,
        recent: true,
    },
]
