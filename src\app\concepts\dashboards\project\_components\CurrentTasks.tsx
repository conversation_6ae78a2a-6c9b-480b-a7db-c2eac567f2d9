'use client'

import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'
import Tag from '@/components/ui/Tag'
import Avatar from '@/components/ui/Avatar'

interface CurrentTasksProps {
    data: Array<{
        id: string
        name: string
        assignee: {
            name: string
            img: string
        }
        dueDate: number
        status: number
        priority: number
        progress: number
        labels: string[]
    }>
}

const CurrentTasks = ({ data }: CurrentTasksProps) => {
    const getStatusColor = (status: number) => {
        switch (status) {
            case 0:
                return 'bg-amber-100 text-amber-600 border-amber-200'
            case 1:
                return 'bg-blue-100 text-blue-600 border-blue-200'
            case 2:
                return 'bg-emerald-100 text-emerald-600 border-emerald-200'
            default:
                return 'bg-gray-100 text-gray-600 border-gray-200'
        }
    }

    const getStatusText = (status: number) => {
        switch (status) {
            case 0:
                return 'To Do'
            case 1:
                return 'In Progress'
            case 2:
                return 'Completed'
            default:
                return 'Unknown'
        }
    }

    const getPriorityColor = (priority: number) => {
        switch (priority) {
            case 0:
                return 'bg-red-100 text-red-600 border-red-200'
            case 1:
                return 'bg-amber-100 text-amber-600 border-amber-200'
            case 2:
                return 'bg-emerald-100 text-emerald-600 border-emerald-200'
            default:
                return 'bg-gray-100 text-gray-600 border-gray-200'
        }
    }

    const getPriorityText = (priority: number) => {
        switch (priority) {
            case 0:
                return 'High'
            case 1:
                return 'Medium'
            case 2:
                return 'Low'
            default:
                return 'Unknown'
        }
    }

    const formatDate = (timestamp: number) => {
        return new Date(timestamp).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        })
    }

    return (
        <Card>
            <h5 className="mb-6">Current Tasks</h5>
            <div className="space-y-4">
                {data?.slice(0, 5).map((task) => (
                    <div key={task.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                                <h6 className="font-medium mb-2">{task.name}</h6>
                                <div className="flex items-center gap-2 mb-2">
                                    <Tag className={`${getStatusColor(task.status)} border text-xs`}>
                                        {getStatusText(task.status)}
                                    </Tag>
                                    <Tag className={`${getPriorityColor(task.priority)} border text-xs`}>
                                        {getPriorityText(task.priority)}
                                    </Tag>
                                </div>
                            </div>
                            <Avatar
                                src={task.assignee.img}
                                size="sm"
                                className="bg-primary text-white"
                            >
                                {task.assignee.name.charAt(0)}
                            </Avatar>
                        </div>

                        <div className="mb-3">
                            <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-gray-500">Progress</span>
                                <span className="text-sm font-medium">{task.progress}%</span>
                            </div>
                            <Progress
                                percent={task.progress}
                                showInfo={false}
                                size="sm"
                                customColorClass={
                                    task.progress >= 80 ? 'bg-emerald-500' :
                                    task.progress >= 50 ? 'bg-blue-500' : 'bg-amber-500'
                                }
                            />
                        </div>

                        <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Due: {formatDate(task.dueDate)}</span>
                            <span className="text-gray-500">{task.assignee.name}</span>
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button className="text-sm text-primary hover:text-primary-dark font-medium">
                    View all tasks
                </button>
            </div>
        </Card>
    )
}

export default CurrentTasks
