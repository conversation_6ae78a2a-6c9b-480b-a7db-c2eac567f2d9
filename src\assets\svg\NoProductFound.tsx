const NoProductFound = ({
    height = 100,
    width = 100,
}: {
    height?: number
    width?: number
}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlSpace="preserve"
            x={0}
            y={0}
            viewBox="0 0 400 400"
            width={width}
            height={height}
        >
            <style>
                {
                    '.st0{fill:#bababa}.st3{fill:#cecece}.st4{fill:#45a2ff}.st5{fill:#fff}'
                }
            </style>
            <path
                d="M64.5 143.8c0-.1 0-.1 0 0-.1-.1-.1-.1 0 0-.7 2.4-2.3 4.2-4.7 5.3 2.4 1.1 4 2.8 4.7 5.3v.1-.1c.7-2.4 2.2-4.2 4.7-5.3-2.5-1.1-4.1-2.9-4.7-5.3zM341.8 102.8c0-.1 0 0 0 0s0-.1 0 0c-.7 2.4-2.3 4.2-4.7 5.3 2.4 1.1 4 2.8 4.7 5.3v.1-.1c.7-2.4 2.2-4.2 4.7-5.3-2.4-1.1-4-2.9-4.7-5.3zM281.2 62.7c-.7 2.4-2.3 4.2-4.7 5.3 2.4 1.1 4 2.8 4.7 5.3v.1-.1c.7-2.4 2.2-4.2 4.7-5.3-2.4-1.1-4-2.8-4.7-5.3zM138.3 40.2c-.7 2.4-2.3 4.2-4.7 5.3 2.4 1.1 4 2.8 4.7 5.3v.1-.1c.7-2.4 2.2-4.2 4.7-5.3-2.4-1.1-4-2.8-4.7-5.3zM337.1 32.6v-.1.1-.1.1c-1.4 4.9-4.5 8.4-9.4 10.5 4.9 2.1 8 5.6 9.4 10.5v.1-.1.1-.1c1.4-4.9 4.5-8.4 9.4-10.5-4.9-2.1-8-5.6-9.4-10.5zM39.8 63.6v-.1.1-.1.1c-1.4 4.9-4.5 8.4-9.4 10.5 4.9 2.1 8 5.6 9.4 10.5v.1-.1.1-.1c1.4-4.9 4.5-8.4 9.4-10.5-4.9-2.1-8.1-5.6-9.4-10.5z"
                className="fill-[#bababa]"
            />
            <circle
                cx={169.8}
                cy={113.5}
                r={30.6}
                className="fill-[#bababa]"
                transform="rotate(-18.72 169.847 113.55)"
            />
            <path
                d="M123.6 140.7c-2.7 0-4.3-.6-4.9-1.9-.8-1.8.9-4.3 5-7.7 3.7-3 9.3-6.7 16.2-10.6.3-.2.8-.1 1 .3.2.3.1.8-.3 1-17.6 10-21.2 15-20.6 16.5.6 1.3 5.3 1.7 16.8-1.3 10.4-2.7 23.7-7.6 37.4-13.6s26.3-12.6 35.3-18.4c4.4-2.8 7.7-5.3 9.8-7.4 2.7-2.7 2.7-3.8 2.5-4.1-.6-1.4-6.7-2.2-25.7 3.9-.4.1-.8-.1-.9-.5-.1-.4.1-.8.5-.9 7.4-2.4 13.8-4 18.5-4.7 5.3-.7 8.2-.2 9 1.5.6 1.4-.3 3.3-2.8 5.8-2.2 2.1-5.6 4.7-10 7.5-9.1 5.8-21.7 12.4-35.5 18.5s-27.2 10.9-37.6 13.7c-5.1 1.3-9.3 2.1-12.3 2.3-.6.1-1 .1-1.4.1z"
                style={{
                    fill: '#8c8c8c',
                }}
            />
            <path
                d="M230.1 365.8c6.5-14.1 10.1-29.7 10.1-46.2 0-61.3-49.7-110.9-110.9-110.9-61.3 0-110.9 49.7-110.9 110.9 0 16.5 3.6 32.2 10.1 46.2h201.6z"
                style={{
                    fill: '#e2e2e2',
                }}
            />
            <path
                d="M222.2 258.9c-9.6 2.7-23.5 4.6-43 3.2-38.3-2.6-84.3-24.6-120.4-28.2-22.4 18.5-37.4 45.6-40 76.3 10.9-9.1 26.8-18.9 48.9-24.6 49.1-12.7 103.9 1.8 132.9 0 12.3-.8 23.1-4.4 31.4-8.3-2.8-6.5-6-12.6-9.8-18.4zM212.2 329.9C194 329.1 130 296.3 78 309c-29.1 7.1-47.9 16.7-58.9 23.7 1.4 11.7 4.6 22.8 9.3 33h201.7c6.3-13.7 9.9-28.9 10.1-44.9-8.6 4.9-19.2 9.5-28 9.1z"
                className="fill-[#cecece]"
            />
            <circle cx={64.4} cy={214.9} r={24.7} className="fill-[#bababa]" />
            <path
                d="m324.6 285.1 35.2-14c5.1-2 9.5-5.6 12.6-10.2l24.7-36.8c1.8-2.6.7-6.2-2.2-7.5l-20.2-8.8c-2-.9-4.2-.4-5.7 1.1l-16.9 17.7-24.4 3.9-3.1 54.6z"
                className="fill-primary"
            />
            <path d="M324.6 285.8c-.1 0-.3 0-.4-.1-.2-.1-.3-.4-.3-.6l3-54.7c0-.3.3-.6.6-.7l24.2-3.9 16.7-17.5c1.7-1.8 4.3-2.3 6.5-1.3l20.2 8.8c1.6.7 2.8 2 3.3 3.7.5 1.7.2 3.4-.7 4.9L373 261.3c-3.2 4.7-7.6 8.4-12.9 10.5l-35.2 14h-.3zm3.8-54.8-2.9 53 34.1-13.6c5-2 9.3-5.4 12.3-9.9l24.7-36.8c.7-1.1.9-2.4.6-3.7-.4-1.2-1.3-2.3-2.5-2.8l-20.2-8.8c-1.7-.7-3.7-.3-4.9 1l-17 17.6c-.1.1-.3.2-.4.2l-23.8 3.8z" />
            <path
                d="m238.8 247.6-13.6-35.3c-2-5.2-2.4-10.8-1.1-16.2l10.3-43.1c.7-3.1 4.1-4.8 7-3.5l20.2 8.8c2 .9 3.2 2.8 3 5l-1.5 24.4 13.7 20.6-38 39.3z"
                className="fill-primary"
            />
            <path d="M238.8 248.3h-.1c-.2 0-.4-.2-.5-.4l-13.6-35.3c-2.1-5.3-2.4-11.1-1.1-16.6l10.3-43.1c.4-1.7 1.5-3.1 3.1-3.9 1.5-.8 3.3-.8 4.9-.1l20.2 8.8c2.2 1 3.6 3.3 3.5 5.7l-1.5 24.1 13.6 20.4c.2.3.2.7-.1.9l-38 39.4c-.3 0-.5.1-.7.1zm.5-98.6c-.7 0-1.3.2-2 .5-1.2.6-2 1.6-2.3 2.9l-10.3 43.1c-1.3 5.3-.9 10.7 1.1 15.8l13.2 34.3 36.9-38.2-13.4-20.1c-.1-.1-.1-.3-.1-.4l1.5-24.4c.1-1.8-.9-3.5-2.6-4.3l-20.2-8.8c-.6-.2-1.2-.4-1.8-.4z" />
            <path
                d="M273.1 365.8c3.3-3.2 6.8-6.8 10.4-10.8 26.5-29.2 55.7-84.5 64.4-143.9l-35.2-15.4-35.2-15.4c-37.9 46.7-58.5 105.7-62 145-1.5 16.8-1.2 29.6 1.4 40.5h56.2z"
                className="fill-white"
            />
            <path d="M273.1 366.5h-56.2c-.3 0-.6-.2-.7-.6-2.5-10.6-3-23.2-1.4-40.7 1.8-20.7 8.3-45.5 18.1-69.9 11.2-27.7 26.4-53.8 43.9-75.5.2-.3.5-.3.8-.2l70.5 30.8c.3.1.5.4.4.8-4 27.6-12.9 56.5-25.6 83.5-11.2 23.8-25 45.4-39 60.8-3.7 4.1-7.1 7.6-10.4 10.8-.1.1-.2.2-.4.2zm-55.6-1.5h55.3c3.2-3.1 6.5-6.6 10.1-10.6 13.9-15.3 27.6-36.8 38.7-60.4 12.6-26.7 21.3-55.3 25.4-82.6L277.5 181c-17.3 21.5-32.3 47.3-43.3 74.7-9.8 24.3-16.2 48.9-18 69.5-1.5 17.2-1.1 29.5 1.3 39.8z" />
            <circle
                cx={264.3}
                cy={306.3}
                r={27.7}
                className="fill-white"
                transform="rotate(-21.4 264.21 306.28)"
            />
            <path d="M264.2 334.8c-3.9 0-7.7-.8-11.4-2.4-14.4-6.3-21-23.1-14.7-37.5 6.3-14.4 23.1-21 37.5-14.7 7 3 12.3 8.6 15.1 15.7 2.8 7.1 2.6 14.8-.4 21.8s-8.6 12.3-15.7 15.1c-3.3 1.3-6.8 2-10.4 2zm.1-55.5c-3.3 0-6.7.6-9.9 1.9-6.7 2.6-12 7.7-14.9 14.3-2.9 6.6-3 14-.4 20.7 2.6 6.7 7.7 12 14.3 14.9 6.6 2.9 14 3 20.7.4 6.7-2.6 12-7.7 14.9-14.3 2.9-6.6 3-14 .4-20.7-2.6-6.7-7.7-12-14.3-14.9-3.5-1.6-7.2-2.3-10.8-2.3z" />
            <circle
                cx={264.3}
                cy={306.3}
                r={17.6}
                className="fill-white"
                transform="rotate(-45.903 264.235 306.289)"
            />
            <path d="M264.3 324.7c-2.5 0-5-.5-7.3-1.5-4.5-2-8-5.6-9.7-10.1-1.8-4.6-1.7-9.6.3-14l.7.3-.7-.3c4.1-9.3 14.9-13.5 24.2-9.5 9.3 4.1 13.5 14.9 9.5 24.2-2 4.5-5.6 8-10.1 9.7-2.4.8-4.7 1.2-6.9 1.2zm0-35.3c-6.5 0-12.8 3.8-15.5 10.2-1.8 4.1-1.9 8.7-.2 13 1.7 4.2 4.8 7.5 9 9.3 4.1 1.8 8.7 1.9 13 .2 4.2-1.6 7.5-4.8 9.3-9 3.7-8.6-.2-18.6-8.7-22.3-2.4-1-4.7-1.4-6.9-1.4z" />
            <path
                d="m298 151.5 56.9 24.8c3.1 1.4 4.3 5.2 2.5 8.1l-15.2 24.2-59.2-25.9 7.4-27.6c.9-3.3 4.5-5 7.6-3.6z"
                style={{
                    fill: '#393f4f',
                }}
            />
            <path d="M342.2 209.3c-.1 0-.2 0-.3-.1l-59.3-25.9c-.3-.1-.5-.5-.4-.8l7.4-27.6c.5-1.8 1.7-3.3 3.4-4.1 1.7-.8 3.6-.8 5.3-.1l56.9 24.8c1.7.7 3 2.2 3.5 3.9.5 1.8.3 3.7-.7 5.2L342.8 209c-.1.2-.3.3-.6.3zm-58.4-27 58.2 25.4 14.9-23.6c.8-1.2 1-2.7.5-4-.4-1.4-1.4-2.5-2.7-3l-56.9-24.8c-1.3-.6-2.8-.5-4.1.1-1.3.6-2.2 1.8-2.6 3.1l-7.3 26.8z" />
            <path
                d="M318.1 183c3 1.3 4.3 4.7 3 7.7l-31.7 72.6c-1.3 3-4.7 4.3-7.7 3-3-1.3-4.3-4.7-3-7.7l31.7-72.6c1.3-2.9 4.8-4.3 7.7-3z"
                className="fill-primary"
            />
            <path d="M284 267.5c-.9 0-1.8-.2-2.6-.6-3.3-1.4-4.8-5.3-3.4-8.6l31.7-72.6c.7-1.6 2-2.8 3.6-3.5 1.6-.6 3.4-.6 5 .1s2.8 2 3.5 3.6c.6 1.6.6 3.4-.1 5L290 263.5c-.7 1.6-2 2.8-3.6 3.5-.7.4-1.5.5-2.4.5zm31.8-84.3c-.6 0-1.3.1-1.9.4-1.3.5-2.3 1.5-2.8 2.7l-31.7 72.6c-1.1 2.6.1 5.6 2.6 6.7 2.6 1.1 5.6-.1 6.7-2.6l31.7-72.6c.5-1.3.6-2.6.1-3.9-.5-1.3-1.5-2.3-2.7-2.8-.6-.3-1.3-.5-2-.5z" />
            <path
                d="M273.1 365.8c1-1 2.1-2 3.2-3.1-16.8-16.5-37.3-25.3-61.5-26.5-.5 11.7.1 21.2 2.1 29.6h56.2z"
                className="fill-primary"
            />
            <path d="M273.1 366.5h-56.2c-.3 0-.6-.2-.7-.6-2-8.3-2.7-17.8-2.1-29.8 0-.2.1-.4.2-.5.1-.1.3-.2.5-.2 12.1.6 23.5 3.2 33.8 7.6 10.3 4.4 19.8 10.9 28.2 19.1.1.1.2.3.2.5s-.1.4-.2.5c-1.1 1.1-2.1 2.1-3.2 3.1-.2.2-.3.3-.5.3zm-55.6-1.5h55.3l2.4-2.4c-8.2-7.9-17.3-14.1-27.2-18.3-9.9-4.3-20.9-6.8-32.5-7.4-.5 11.3.2 20.2 2 28.1z" />
            <path
                d="M387.4 366.5H6.6c-.4 0-.7-.3-.7-.7 0-.4.3-.7.7-.7h380.8c.4 0 .7.3.7.7 0 .4-.3.7-.7.7z"
                className="fill-[#bababa]"
            />
        </svg>
    )
}

export default NoProductFound
