export const allDemos = [
    {
        id: 'dashboardEcommerce',
        name: 'Ecommerce',
        path: '/dashboards/ecommerce'
    },
    {
        id: 'dashboardProject',
        name: 'Project',
        path: '/dashboards/project'
    },
    {
        id: 'dashboardMarketing',
        name: 'Marketing',
        path: '/dashboards/marketing'
    },
    {
        id: 'dashboardAnalytic',
        name: 'Analytic',
        path: '/dashboards/analytic'
    }
]

export const ecommerceDemos = [
    {
        id: 'dashboardEcommerce',
        name: 'Dashboard',
        path: '/dashboards/ecommerce'
    },
    {
        id: 'productCreate',
        name: 'Create Product',
        path: '/concepts/products/product-create'
    },
    {
        id: 'productEdit',
        name: 'Edit Product',
        path: '/concepts/products/product-edit/12'
    },
    {
        id: 'productList',
        name: 'Product List',
        path: '/concepts/products/product-list'
    },
    {
        id: 'orderCreate',
        name: 'Create Order',
        path: '/concepts/orders/order-create'
    },
    {
        id: 'orderDetail',
        name: 'Order Details',
        path: '/concepts/orders/order-details/95954'
    },
    {
        id: 'orderEdit',
        name: 'Edit Order',
        path: '/concepts/orders/order-edit/95954'
    },
    {
        id: 'orderList',
        name: 'Order List',
        path: '/concepts/orders/order-list'
    },
]

export const marketingDemos = [
    {
        id: 'dashboardMarketing',
        name: 'Dashboard',
        path: '/dashboards/marketing'
    },
    {
        id: 'cutomerList',
        name: 'Customer List',
        path: '/concepts/customers/customer-list'
    },
    {
        id: 'cutomerCreate',
        name: 'Create Customer',
        path: '/concepts/customers/customer-create'
    },
    {
        id: 'cutomerDetails',
        name: 'Customer Details',
        path: '/concepts/customers/customer-details/1'
    },
    {
        id: 'cutomerEdit',
        name: 'Edit Customer',
        path: '/concepts/customers/customer-edit/1'
    }
]

export const projectDemos = [
    {
        id: 'dashboardProject',
        name: 'Dashboard',
        path: '/dashboards/project'
    },
    {
        id: 'projectIssue',
        name: 'Issue',
        path: '/concepts/projects/tasks/1'
    },
    {
        id: 'projectTasks',
        name: 'Tasks',
        path: '/concepts/projects/tasks'
    },
    {
        id: 'projectDetail',
        name: 'Details',
        path: '/concepts/projects/project-details/27'
    },
    {
        id: 'projectList',
        name: 'List',
        path: '/concepts/projects/project-list'
    },
    {
        id: 'projectScrumboard',
        name: 'Scrumboard',
        path: '/concepts/projects/scrum-board'
    },
]

export const aiDemos = [
    {
        id: 'aiChat',
        name: 'Chat',
        path: '/concepts/ai/chat'
    },
    {
        id: 'aiImage',
        name: 'Image',
        path: '/concepts/ai/image'
    },
]

export const appsDemos = [
    {
        id: 'calendar',
        name: 'Calendar',
        path: '/concepts/calendar'
    },
    {   
        id: 'chat',
        name: 'Chat',
        path: '/concepts/chat'
    },
    {
        id: 'email',
        name: 'Email',
        path: '/concepts/email'
    },
    {
        id: 'fileManager',
        name: 'File Manager',
        path: '/concepts/file-manager'
    },
]

export const helpCenterDemos = [
    {
        id: 'helpCenterSupportHub',
        name: 'Support Hub',
        path: '/concepts/help-center/support-hub'
    },
    {
        id: 'helpCenterManageArticle',
        name: 'Manage Article',
        path: '/concepts/help-center/manage-article'
    },
    {
        id: 'helpCenterArticle',
        name: 'Article',
        path: '/concepts/help-center/article/pWBKE_0UiQ'
    },
    {
        id: 'helpCenterArticleEdit',    
        name: 'Article Edit',
        path: 'concepts/help-center/edit-article/pWBKE_0UiQ'
    }
]

export const accountDemos = [
    {
        id: 'accountActivityLog',
        name: 'Activity Log',
        path: '/concepts/account/activity-log'
    },
    {
        id: 'accountPricing',
        name: 'Pricing',
        path: '/concepts/account/pricing'
    },
    {
        id: 'accountSettings',
        name: 'Settings',
        path: '/concepts/account/settings'
    },
    {
        id: 'accountRolesPermission',
        name: 'Roles & Permission',
        path: '/concepts/account/roles-permissions'
    },
]

export const authDemos = [
    {
        id: 'authSimpleSignIn',
        name: 'Sign In - Simple',
        path: '/auth/sign-in-simple'
    },
    {
        id: 'authSideSignUp',
        name: 'Sign Up - Side',
        path: '/auth/sign-up-side'
    },
    {
        id: 'authForgetPasswordSplit',
        name: 'Forget Password - Split',
        path: '/auth/forgot-password-split'
    },
    {
        id: 'authSimpleResetPassword',
        name: 'Reset Password - Simple',
        path: '/auth/reset-password-simple'
    },
]