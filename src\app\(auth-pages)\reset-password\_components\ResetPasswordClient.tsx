'use client'

import ResetPassword from '@/components/auth/ResetPassword'
import { authService } from '@/lib/auth'
// import { useSearchParams } from 'next/navigation' // Not needed for Supabase flow
import type { OnResetPasswordSubmitPayload } from '@/components/auth/ResetPassword'

const ResetPasswordClient = () => {
    // const searchParams = useSearchParams()

    /** Token or Verification Code ensures the request is tied to the correct user */
    // const token = searchParams.get('token') // Not needed for Supabase flow

    const handleResetPassword = async (
        payload: OnResetPasswordSubmitPayload,
    ) => {
        const { values, setSubmitting, setMessage, setResetComplete } = payload
        try {
            setSubmitting(true)
            await authService.updatePassword(values.newPassword)
            setResetComplete?.(true)
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'An error occurred'
            setMessage(errorMessage)
        } finally {
            setSubmitting(false)
        }
    }

    return <ResetPassword onResetPasswordSubmit={handleResetPassword} />
}

export default ResetPasswordClient
