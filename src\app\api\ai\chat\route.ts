import { NextRequest, NextResponse } from 'next/server'
import uniqueId from 'lodash/uniqueId'
import dayjs from 'dayjs'
import sleep from '@/utils/sleep'

export async function POST(req: NextRequest) {
    const { prompt } = await req.json()

    const userPrompt = prompt.toLocaleLowerCase()

    const responseContents = {
        regular: [
            `This is a mock response designed to simulate an AI-generated reply. Please note that this response is not generated by an actual AI model. You can include 'title', 'code', 'list' keywords in your prompt to get different kinds of mock responses.`,
            `You are seeing a mock response for demonstration purposes. This message is not generated by a real AI model, but rather pre-written for this demo. You can include 'title', 'code', 'list' keywords in your prompt to get different kinds of mock responses.`,
            `For demo purposes, this response is a mock message. It showcases what a typical AI reply might look like, but it is not produced by a real AI. You can include 'title', 'code', 'list' keywords in your prompt to get different kinds of mock responses.`,
            `This mock response is part of a demonstration and is not generated by an actual AI. The content here is pre-defined for showcasing purposes. You can include 'title', 'code', 'list' keywords in your prompt to get different kinds of mock responses.`,
            `As part of this demo, you are viewing a mock response. This text is not created by an AI model, but rather a pre-written example to illustrate functionality. You can include 'title', 'code', 'list' keywords in your prompt to get different kinds of mock responses.`,
        ],
        title: 'This is a mock response with a title.\n\n###### Example Title\n\nPlease note that this is not generated by an actual AI model.',
        code: "This is a mock response with a code snippet.\n\n```\nfunction example() {\n    console.log('This is a mock code snippet.');\n}\n```\n\nPlease note that this is not generated by an actual AI model.",
        list: 'This is a mock response with a list.\n\n1. First item\n2. Second item\n3. Third item\n\nPlease note that this is not generated by an actual AI model.',
    }

    let content =
        responseContents.regular[
            Math.floor(Math.random() * responseContents.regular.length)
        ]

    if (userPrompt.includes('title')) {
        content = responseContents.title
    }
    if (userPrompt.includes('code')) {
        content = responseContents.code
    }
    if (userPrompt.includes('list')) {
        content = responseContents.list
    }

    const response = {
        id: uniqueId('ai-conversation-'),
        choices: [
            {
                finish_reason: 'stop',
                index: 0,
                message: {
                    content: content,
                    role: 'assistant',
                },
            },
        ],
        created: dayjs().unix(),
        model: 'gpt-4',
    }

    await sleep(200)

    return NextResponse.json(response)
}
