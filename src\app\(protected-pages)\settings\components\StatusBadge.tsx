'use client'

import { ReactNode } from 'react'
import classNames from '@/utils/classNames'

interface StatusBadgeProps {
    children: ReactNode
    className?: string
}

const StatusBadge = ({ children, className }: StatusBadgeProps) => {
    return (
        <div className={classNames('px-2 py-1 rounded-md text-xs font-medium', className)}>
            {children}
        </div>
    )
}

export default StatusBadge
