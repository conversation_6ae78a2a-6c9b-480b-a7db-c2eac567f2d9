'use client'

import Card from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'

interface LeadPerformanceProps {
    data: {
        categories: string[]
        series: number[]
    }
}

const LeadPerformance = ({ data }: LeadPerformanceProps) => {
    return (
        <Card>
            <h5 className="mb-6">Lead Performance</h5>
            <Chart
                type="radar"
                height={300}
                series={[
                    {
                        name: 'Performance Score',
                        data: data?.series || []
                    }
                ]}
                customOptions={{
                    xaxis: {
                        categories: data?.categories || []
                    },
                    colors: ['#3B82F6'],
                    fill: {
                        opacity: 0.2
                    },
                    markers: {
                        size: 4
                    },
                    stroke: {
                        width: 2
                    }
                }}
            />
            <div className="mt-4 text-center">
                <div className="text-sm text-gray-500">
                    Performance metrics across key lead indicators
                </div>
            </div>
        </Card>
    )
}

export default LeadPerformance
