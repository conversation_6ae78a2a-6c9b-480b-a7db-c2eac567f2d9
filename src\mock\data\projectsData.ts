const y = new Date().getFullYear()

export const projectDetailsData = {
    content: `<h5>Project overview</h5><p>Bite plants chirp at birds for gnaw the corn cob be a nyan cat, feel great about it, be annoying 24/7 poop rainbows in litter box all day sleep on my human's head cat dog hate mouse eat string barf pillow no baths hate everything cat not kitten around . Swat turds around the house chase dog then run away but pet right here, no not there, here,</p><h5>About the client</h5><p>Lick butt. Under the bed purrrrrr or attack like a vicious monster and instantly break out into full speed gallop across the house for no reason for scream for no reason at 4 am or climb leg meowing chowing and wowing. Cat gets stuck in tree firefighters try to get cat down firefighters get stuck in tree cat eats firefighters' slippers pelt around the house and up and down stairs chasing phantoms for sleep over your phone and make cute snoring noises take a deep sniff of sock then walk around with mouth half open drool sit in box, pee on walls it smells like breakfast. Cough furball kitty poochy.</p><ul><li>What the heck just happened, something feels fishy head nudges i cry and cry and cry unless you pet me, and then maybe i cry just for fun demand to have some of whatever the human is cooking, then sniff the offering and walk away lick the curtain just to be annoying the door is opening! how exciting oh, it's you, meh run off table persian cat jump eat fish. Knock dish off table head butt cant eat out of my own dish touch my tail, i shred your hand purrrr.</li><li>Pounce on unsuspecting person purr as loud as possible, be the most annoying cat that you can, and, knock everything off the table but flex claws on the human's belly and purr like a lawnmower, yet ask for petting so cats are a queer kind of folk so lick yarn hanging out of own butt yet skid on floor, crash into wall.</li><li>Meow meow you are my owner so here is a dead rat get scared by sudden appearance of cucumber leave dead animals as gifts cat jumps and falls onto the couch purrs and wakes up in a new dimension filled with kitty litter meow meow yummy there is a bunch of cats hanging around eating catnip </li></ul><img src="/img/others/article-img-1.jpg" alt="" /><h5>The goals</h5><p>Reward the chosen human with a slow blink making sure that fluff gets into the owner's eyes and sit on human cuddle no cuddle cuddle love scratch scratch. Grass smells good then cats take over the world but stare at ceiling light, sniff other cat's butt and hang jaw half open thereafter the dog smells bad. Groom yourself 4 hours - checked, have your beauty sleep 18 hours - checked, be fabulous for the rest of the day - checked shed everywhere shed everywhere stretching attack your ankles chase the red dot, hairball run catnip eat the grass sniff for bathe private parts with tongue then lick owner's face hit you unexpectedly. Growl at dogs in my sleep scratch the furniture inspect anything brought into the house, for pelt around the house and up and down stairs chasing phantoms x rub my belly hiss chew the plant. Licks your face mrow stare at the wall, play with food and get confused by dust. Commence midnight zoomies destroy house in 5 seconds, suddenly go on wild-eyed crazy rampage. </p><h5>Requirements</h5><p>Floof tum, tickle bum, jellybean footies curly toes no, you can't close the door, i haven't decided whether or not i wanna go out, yet hiding behind the couch until lured out by a feathery toy so chase red laser dot. Eat my own ears catching very fast laser pointer but eat fish on floor cough furball into food bowl then scratch owner for a new one use lap as chair reaches under door into adjacent room. Eat the fat cats food paw your face to wake you up in the morning. While happily ignoring when being called.</p>`,
    activities: [],
    members: [],
    tasks: [],
    client: {
        clientName: 'Acme Agency inc.',
        skateHolder: {
            name: 'Jeremiah Minsk',
            img: '/img/avatars/thumb-2.jpg',
        },
        projectManager: {
            name: 'Max Alexander',
            img: '/img/avatars/thumb-3.jpg',
        },
    },
    schedule: {
        startDate: **********,
        dueDate: **********,
        status: 'In progress',
        completion: 80,
    },
}

export const projectListData = [
    {
        id: '27',
        name: 'EVO SaaS',
        category: 'Web Application',
        desc: 'Most of you are familiar with the virtues of a programmer',
        attachmentCount: 12,
        totalTask: 32,
        completedTask: 27,
        progression: 80,
        dayleft: 21,
        favourite: true,
        member: [
            {
                name: 'Roberta Horton',
                img: '/img/avatars/thumb-8.jpg',
            },
            {
                name: 'Eugene Stewart',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        // Add detailed project data for project details page
        content: `<h5>Project overview</h5><p>Site plants chirp at birds for gnaw the corn cob be a nyan cat, feel great about it, be annoying 24/7 poop rainbows in litter box all day sleep on my human's head cat dog hate mouse eat string barf pillow no baths hate everything cat not kitten around . Swat turds around the house chase dog then run away but pet right here, no not there, here,</p><h5>About the client</h5><p>Lick butt. Under the bed purrrrrr or attack like a vicious monster and instantly break out into full speed gallop across the house for no reason for scream for no reason at 4 am or climb leg meowing chowing and wowing. Cat gets stuck in tree firefighters try to get cat down firefighters get stuck in tree cat eats firefighters' slippers pelt around the house and up and down stairs chasing phantoms for sleep over your phone and make cute snoring noises take a deep sniff of sock then walk around with mouth half open drool sit in box, pee on walls it smells like breakfast. Cough furball kitty poochy.</p><p>What the heck just happened, something feels fishy head nudges i cry and cry and cry unless you pet me, and then maybe i cry just for fun demand to have some of whatever the human is cooking, then sniff the offering and walk away lick the curtain just to be annoying the door is opening! how exciting oh, it's you, meh run off table persian cat jump eat fish. Knock dish off table head butt cant eat out of my own dish touch my tail, i shred your hand purrrr.</p><p>Pounce on unsuspecting person purr as loud as possible, be the most annoying cat that you can, and, knock everything off the table but flex claws on the human's belly and purr like a lawnmower, yet ask for petting so cats are a queer kind of folk so lick yarn hanging out of own butt yet skid on floor, crash into wall.</p><p>Meow meow you are my owner so here is a dead rat get scared by sudden appearance of cucumber leave dead animals as gifts cat jumps and falls onto the couch purrs and wakes up in a new dimension filled with kitty litter meow meow yummy there is a bunch of cats hanging around eating catnip </p><img src="/img/others/article-img-1.jpg" alt="" /><h5>The goals</h5><p>Reward the chosen human with a slow blink making sure that fluff gets into the owner's eyes and sit on human cuddle no cuddle cuddle love scratch scratch. Grass smells good then cats take over the world but stare at ceiling light, sniff other cat's butt and hang jaw half open thereafter the dog smells bad. Groom yourself 4 hours - checked, have your beauty sleep 18 hours - checked, be fabulous for the rest of the day - checked shed everywhere shed everywhere stretching attack your ankles chase the red dot, hairball run catnip eat the grass sniff for bathe private parts with tongue then lick owner's face hit you unexpectedly. Growl at dogs in my sleep scratch the furniture inspect anything brought into the house, for pelt around the house and up and down stairs chasing phantoms x rub my belly hiss chew the plant. Licks your face mrow stare at the wall, play with food and get confused by dust. Commence midnight zoomies destroy house in 5 seconds, suddenly go on wild-eyed crazy rampage. </p>`,
        client: {
            clientName: 'Acme Agency Inc.',
            skateHolder: {
                name: 'Jeremiah Minsk',
                img: '/img/avatars/thumb-2.jpg',
            },
            projectManager: {
                name: 'Max Alexander',
                img: '/img/avatars/thumb-3.jpg',
            },
        },
        schedule: {
            startDate: **********,
            dueDate: **********,
            status: 'In progress',
            completion: 80,
        },
    },
    {
        id: '28',
        name: 'AIA Bill App',
        category: 'Mobile Application',
        desc: 'We are not shipping your machine!',
        attachmentCount: 5,
        totalTask: 36,
        completedTask: 15,
        progression: 45,
        dayleft: 19,
        favourite: true,
        member: [
            {
                name: 'Angelina Gotelli',
                img: '/img/avatars/thumb-1.jpg',
            },
        ],
        content: `<h5>Project overview</h5><p>AIA Bill App is a comprehensive mobile billing solution designed to streamline invoice management and payment processing. This innovative application provides businesses with powerful tools to create, send, and track invoices while offering customers convenient payment options.</p><h5>About the client</h5><p>AIA Corporation is a leading technology company specializing in business automation solutions. They have been in the market for over 15 years and serve thousands of small to medium-sized businesses across various industries. Their focus on user-friendly interfaces and robust functionality has made them a trusted partner for business digitization.</p><p>The client approached us with a need to modernize their billing system and provide their customers with a mobile-first solution. They wanted to reduce paper-based processes and improve payment collection efficiency while maintaining the highest security standards.</p><img src="/img/others/article-img-1.jpg" alt="" /><h5>The goals</h5><p>The primary goal is to create a user-friendly mobile application that simplifies the billing process for businesses while providing customers with multiple payment options. The app should integrate seamlessly with existing accounting systems and provide real-time analytics and reporting capabilities.</p>`,
        client: {
            clientName: 'AIA Corporation',
            skateHolder: {
                name: 'Angelina Gotelli',
                img: '/img/avatars/thumb-1.jpg',
            },
            projectManager: {
                name: 'Shannon Baker',
                img: '/img/avatars/thumb-4.jpg',
            },
        },
        schedule: {
            startDate: **********,
            dueDate: **********,
            status: 'In progress',
            completion: 45,
        },
    },
    {
        id: '29',
        name: 'IOP Web',
        category: 'Web Backend Application',
        desc: 'There are two ways to write error-free programs; only the third one works.',
        attachmentCount: 8,
        totalTask: 27,
        completedTask: 19,
        progression: 73,
        dayleft: 6,
        favourite: false,
        member: [
            {
                name: 'Max Alexander',
                img: '/img/avatars/thumb-3.jpg',
            },
            {
                name: 'Camila Simmmons',
                img: '/img/avatars/thumb-9.jpg',
            },
        ],
    },
    {
        id: '31',
        name: 'Octonine POS',
        category: 'Backend Application',
        desc: 'Everything that can be invented has been invented.',
        attachmentCount: 8,
        totalTask: 78,
        completedTask: 23,
        progression: 21,
        dayleft: 52,
        favourite: true,
        member: [
            {
                name: 'Earl Miles',
                img: '/img/avatars/thumb-10.jpg',
            },
            {
                name: 'Roberta Horton',
                img: '/img/avatars/thumb-8.jpg',
            },
            {
                name: 'Arlene Pierce',
                img: '/img/avatars/thumb-6.jpg',
            },
            {
                name: 'Steve Sutton',
                img: '/img/avatars/thumb-11.jpg',
            },
            {
                name: 'Alvin Moreno',
                img: '/img/avatars/thumb-14.jpg',
            },
        ],
    },
    {
        id: '30',
        name: 'Evo SaaS API',
        category: 'Backend Services',
        desc: 'Debugging is twice as hard as writing the code in the first place.',
        attachmentCount: 2,
        totalTask: 15,
        completedTask: 13,
        progression: 87,
        dayleft: 2,
        favourite: true,
        member: [
            {
                name: 'Steve Sutton',
                img: '/img/avatars/thumb-11.jpg',
            },
        ],
    },
    {
        id: '32',
        name: 'Posiflex Web',
        category: 'Frontend Web Application',
        desc: 'The function of good software is to make the complex appear to be simple.',
        attachmentCount: 6,
        totalTask: 18,
        completedTask: 9,
        progression: 50,
        dayleft: 6,
        favourite: false,
        member: [
            {
                name: 'Miriam Herrera',
                img: '/img/avatars/thumb-12.jpg',
            },
        ],
        content: `<h5>Project overview</h5><p>Posiflex Web is a comprehensive frontend web application designed to modernize point-of-sale systems for retail businesses. This project focuses on creating an intuitive, responsive interface that simplifies complex retail operations while maintaining powerful functionality.</p><h5>About the client</h5><p>Posiflex Technology is a global leader in POS hardware and software solutions, serving retail and hospitality industries worldwide. With over 30 years of experience, they are committed to innovation and providing cutting-edge technology that enhances business efficiency.</p><p>The client needed a modern web interface to complement their existing POS hardware, enabling businesses to manage inventory, sales, and customer data through a user-friendly web platform. The goal was to create a seamless experience that works across all devices and screen sizes.</p><img src="/img/others/article-img-1.jpg" alt="" /><h5>The goals</h5><p>Create a responsive, modern web application that integrates seamlessly with existing POS hardware. The application should provide real-time inventory management, sales analytics, and customer relationship management tools while maintaining the simplicity that makes complex operations appear effortless.</p>`,
        client: {
            clientName: 'Posiflex Technology',
            skateHolder: {
                name: 'Miriam Herrera',
                img: '/img/avatars/thumb-12.jpg',
            },
            projectManager: {
                name: 'Eugene Stewart',
                img: '/img/avatars/thumb-5.jpg',
            },
        },
        schedule: {
            startDate: **********,
            dueDate: **********,
            status: 'In progress',
            completion: 50,
        },
    },
    {
        id: '33',
        name: 'FoksMart APP',
        category: 'Mobile Application',
        desc: 'It is not about bits, bytes and protocols, but profits, losses and margins. ',
        attachmentCount: 3,
        totalTask: 26,
        completedTask: 19,
        progression: 67,
        dayleft: 14,
        favourite: false,
        member: [
            {
                name: 'Shannon Baker',
                img: '/img/avatars/thumb-4.jpg',
            },
            {
                name: 'Jessica Wells',
                img: '/img/avatars/thumb-8.jpg',
            },
        ],
    },
    {
        id: '34',
        name: 'FlowBuzz SEO',
        category: 'Marketing',
        desc: 'Destroy couch lick the plastic bag for stare at owner accusingly then wink. ',
        attachmentCount: 3,
        totalTask: 26,
        completedTask: 19,
        progression: 88,
        dayleft: 18,
        favourite: false,
        member: [
            {
                name: 'Angelina Gotelli',
                img: '/img/avatars/thumb-1.jpg',
            },
            {
                name: 'Roberta Horton',
                img: '/img/avatars/thumb-7.jpg',
            },
        ],
    },
    {
        id: '35',
        name: 'Mind Blast APP',
        category: 'Mobile Application',
        desc: 'Loves cheeseburgers suddenly go on wild-eyed crazy rampage drink from the toilet.',
        attachmentCount: 3,
        totalTask: 74,
        completedTask: 31,
        progression: 42,
        dayleft: 37,
        favourite: false,
        member: [
            {
                name: 'Cassandra Murray',
                img: '/img/avatars/thumb-13.jpg',
            },
            {
                name: 'Jackie Soto',
                img: '/img/avatars/thumb-15.jpg',
            },
        ],
    },
]

const toBeProcessCards = [
    {
        id: 'zb7zxtjctd',
        name: 'Unable to upload file',
        description:
            'Scenester hashtag sustainable art party 3 wolf moon live-edge portland offal master cleanse disrupt viral palo santo tacos. Offal sriracha you probably havent heard of them vaporware glossier.',
        cover: '/img/others/img-13.jpg',
        members: [
            {
                id: '1',
                name: 'Angelina Gotelli',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-1.jpg',
            },
            {
                id: '2',
                name: 'Jeremiah Minsk',
                email: '',
                img: '/img/avatars/thumb-2.jpg',
            },
            {
                id: '3',
                name: 'Max Alexander',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        labels: ['Task', 'Live issue'],
        attachments: [
            {
                id: 'jubuK7XGp3',
                name: 'mail.jpg',
                src: '/img/others/img-13.jpg',
                size: '36.1kb',
            },
            {
                id: 'xsb3HCejCM',
                name: 'mail.jpg',
                src: '/img/others/img-14.jpg',
                size: '55.9kb',
            },
        ],
        comments: [
            {
                id: 'R22TqMkACm',
                name: 'Camila Simmmons',
                src: '/img/avatars/thumb-9.jpg',
                message:
                    'I am baby kitsch plaid mustache, williamsburg butcher gluten-free 3 wolf moon authentic quinoa selvage knausgaard unicorn. Palo santo viral everyday carry, heirloom tumblr raw denim yr iceland wayfarers single-origin coffee tote bag shoreditch cloud bread poke.',
                date: new Date(y, 4, 20),
            },
        ],
        dueDate: new Date(y, 7, 5),
        checked: false,
    },
    {
        id: '7qgsduurxt',
        name: 'Table data incorrect',
        description:
            'Extra  breve french press organic milk lungo bar. At, carajillo, saucer latte americano and shop rich french press. And barista mazagran, black, cup extraction ristretto plunger pot cinnamon café au lait.',
        cover: '',
        members: [
            {
                id: '8',
                name: 'Jessica Wells',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-8.jpg',
            },
        ],
        labels: ['Bug'],
        attachments: [],
        comments: [
            {
                id: 'Wx8FDSsVTg',
                name: 'Arlene Pierce',
                src: '/img/avatars/thumb-6.jpg',
                message:
                    'Helvetica 8-bit photo booth tumblr food truck. Enamel pin wolf tousled sartorial, brunch shoreditch skateboard beard helvetica. Plaid typewriter gastropub bespoke.',
                date: new Date(y, 4, 20),
            },
            {
                id: '3AhkqqSFFr',
                name: 'Max Alexander',
                src: '/img/avatars/thumb-3.jpg',
                message:
                    'Af trust fund meggings, meditation chicharrones brunch wolf narwhal shoreditch whatever bicycle rights ramps. ',
                date: new Date(y, 4, 20),
            },
        ],
        dueDate: new Date(y, 6, 11),
    },
    {
        id: 'wtwgpz6csc',
        name: 'Fix broken UI',
        description:
            'Air plant subway tile four loko ramps. Microdosing offal tote bag, single-origin coffee biodiesel before they sold out swag pok pok gastropub tacos letterpress.',
        cover: '',
        members: [
            {
                id: '13',
                name: 'Cassandra Murray',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-13.jpg',
            },
            {
                id: '5',
                name: 'Eugene Stewart',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        labels: ['Low priority'],
        attachments: [],
        comments: [
            {
                id: 'bAvBdtKHNC',
                name: 'Steve Sutton',
                src: '/img/avatars/thumb-11.jpg',
                message:
                    'Helvetica 8-bit photo booth tumblr food truck. Enamel pin wolf tousled sartorial, brunch shoreditch skateboard beard helvetica. Plaid typewriter gastropub bespoke.',
                date: new Date(y, 4, 20),
            },
        ],
        dueDate: new Date(y, 7, 5),
        checked: false,
    },
]

const processingCards = [
    {
        id: 'ywejrdr3rn',
        name: 'Fix dashboard layout',
        description:
            'Biodiesel selvage letterpress 8-bit, coloring book banjo austin pabst post-ironic try-hard gluten-free tilde authentic prism man braid.',
        cover: '',
        members: [
            {
                id: '2',
                name: 'Jeremiah Minsk',
                email: '',
                img: '/img/avatars/thumb-2.jpg',
            },
            {
                id: '8',
                name: 'Jessica Wells',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-8.jpg',
            },
        ],
        labels: ['Bug'],
        attachments: [],
        comments: [
            {
                id: 'afPugkEmkp',
                name: 'Angelina Gotelli',
                src: '/img/avatars/thumb-1.jpg',
                message: '',
                date: new Date(y, 5, 16),
            },
        ],
        dueDate: new Date(y, 3, 17),
        checked: false,
    },
    {
        id: 'tkBXWJGwkr',
        name: 'New design',
        description:
            'Typewriter hell of cloud bread health goth beard mlkshk four loko.',
        cover: '',
        members: [
            {
                id: '10',
                name: 'Earl Miles',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-10.jpg',
            },
        ],
        labels: ['Task'],
        attachments: [
            {
                id: 'NjHJhHeWrG',
                name: 'issue.jpg',
                src: '/img/others/img-16.jpg',
                size: '46.1kb',
            },
        ],
        comments: [
            {
                id: 'MAsPDzGwnA',
                name: 'Max Alexander',
                src: '/img/avatars/thumb-3.jpg',
                message:
                    'Af trust fund meggings, meditation chicharrones brunch wolf narwhal shoreditch whatever bicycle rights ramps. ',
                date: new Date(y, 4, 20),
            },
        ],
        dueDate: null,
        checked: false,
    },
    {
        id: 'VQgUDrYJYH',
        name: 'Improve user experiences',
        description:
            'Java, barista, single origin, aged foam, id dripper organic at grounds id turkish. Grounds french press viennese, strong dark that extra  spoon café au lait cappuccino. Doppio, coffee, affogato, skinny eu latte, ut carajillo black crema instant dark.',
        cover: '',
        members: [
            {
                id: '6',
                name: 'Arlene Pierce',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-6.jpg',
            },
        ],
        labels: ['Low priority', 'Task'],
        attachments: [],
        comments: [],
        dueDate: new Date(y, 4, 20),
        checked: false,
    },
]

const submittedCards = [
    {
        id: 'jzjn95g3v4',
        name: 'Update node environment',
        description:
            'Unicorn occupy locavore pug, stumptown literally tofu irony. Af street art paleo shoreditch. Banh mi before they sold out activated charcoal.',
        cover: '',
        members: [
            {
                id: '3',
                name: 'Max Alexander',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        labels: ['Low priority'],
        attachments: [],
        comments: [
            {
                id: 'nBAGhJqe9v',
                name: 'Max Alexander',
                src: '/img/avatars/thumb-3.jpg',
                message: '',
                date: new Date(y, 1, 8),
            },
        ],
        dueDate: null,
        checked: false,
    },
    {
        id: 'ZFQDPmscwA',
        name: 'Remove user data',
        description:
            'Crucifix polaroid hot chicken asymmetrical wolf helvetica keytar fashion axe ramps YOLO wayfarers 90s.',
        cover: '/img/others/img-15.jpg',
        members: [
            {
                id: '9',
                name: 'Camila Simmmons',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-9.jpg',
            },
            {
                id: '10',
                name: 'Earl Miles',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-10.jpg',
            },
        ],
        labels: ['Live issue'],
        attachments: [
            {
                id: 'NjHJhHeWrG',
                name: 'issue.jpg',
                src: '/img/others/img-15.jpg',
                size: '42.6kb',
            },
        ],
        comments: [
            {
                id: 'nBAGhJqe9v',
                name: 'Max Alexander',
                src: '/img/avatars/thumb-3.jpg',
                message:
                    'Lorem Khaled Ipsum is a major key to success. They key is to have every key, the key to open every door. Im up to something. Life is what you make it, so lets make it.',
                date: new Date(y, 1, 8),
            },
        ],
        dueDate: null,
        checked: false,
    },
]

const completedCards = [
    {
        id: 'yhjk5679xr',
        name: 'Ready to test',
        description:
            'Yr green juice health goth, williamsburg four dollar toast shabby chic meggings jean shorts VHS.',
        cover: '',
        members: [
            {
                id: '13',
                name: 'Cassandra Murray',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-13.jpg',
            },
            {
                id: '9',
                name: 'Camila Simmmons',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-9.jpg',
            },
        ],
        labels: ['Task'],
        attachments: [],
        comments: [
            {
                id: 'yxc5gwrXUZ',
                name: 'Max Alexander',
                src: '/img/avatars/thumb-3.jpg',
                message:
                    'True innovation often comes from the small startup who is lean enough to launch a market but lacks the heft to own it.',
                date: new Date(y, 3, 4),
            },
        ],
        dueDate: new Date(y, 3, 4),
        checked: true,
    },
    {
        id: 'UMgvapYVXm',
        name: 'Slow API connection',
        description:
            'Yr green juice health goth, williamsburg four dollar toast shabby chic meggings jean shorts VHS.',
        cover: '',
        members: [
            {
                id: '5',
                name: 'Eugene Stewart',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-5.jpg',
            },
            {
                id: '6',
                name: 'Arlene Pierce',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-6.jpg',
            },
            {
                id: '7',
                name: 'Roberta Horton',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-7.jpg',
            },
        ],
        labels: ['Bug'],
        attachments: [],
        comments: [],
        dueDate: new Date(y, 7, 19),
        checked: true,
    },
    {
        id: 'uRZNVsCmDW',
        name: 'Login failed',
        description:
            'Air plant subway tile four loko ramps. Microdosing offal tote bag, single-origin coffee biodiesel before they sold.',
        cover: '',
        members: [
            {
                id: '4',
                name: 'Shannon Baker',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-4.jpg',
            },
        ],
        labels: ['Live issue'],
        attachments: [],
        comments: [],
        dueDate: new Date(y, 4, 6),
        checked: true,
    },
    {
        id: 'PBSGmhVgvS',
        name: 'Locale incorrect',
        description:
            'Hoodie mustache woke pour-over you probably havent heard of them cray.',
        cover: '',
        members: [
            {
                id: '5',
                name: 'Eugene Stewart',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-5.jpg',
            },
            {
                id: '3',
                name: 'Max Alexander',
                email: '<EMAIL>',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        labels: ['Low priority'],
        attachments: [],
        comments: [
            {
                id: 'dNskbPFeQD',
                name: 'Max Alexander',
                src: '/img/avatars/thumb-3.jpg',
                message:
                    'Af trust fund meggings, meditation chicharrones brunch wolf narwhal shoreditch whatever bicycle rights ramps. ',
                date: new Date(y, 4, 20),
            },
            {
                id: 'qNzkmRZiTO',
                name: 'Jeremiah Minsk',
                src: '/img/avatars/thumb-2.jpg',
                message: 'Noted!',
                date: new Date(y, 4, 20),
            },
        ],
        dueDate: new Date(y, 7, 13),
        checked: true,
    },
]

export const issueData = {
    ticketId: '#PD-127',
    title: 'API not working',
    createdBy: 'Angelina Gotelli',
    underProject: 'Sprint 2',
    status: 'In progress',
    priority: 'High',
    description: `<p>Leverage agile frameworks to provide a robust synopsis for high level overviews. Iterative approaches to corporate strategy foster collaborative thinking to further the overall value proposition. Organically grow the holistic world view of disruptive innovation via workplace diversity and empowerment.</p>
	<p>Enslave the hooman pounce on unsuspecting person or tuxedo cats always looking dapper yet poop on grasses or growl at dogs in my sleep but love blinks and purr purr purr purr yawn.</p>
	<p>Decide to want nothing to do with my owner today being gorgeous with belly side up claw your carpet in places everyone can see - why hide my amazing artistic clawing skills?</p>
	<p>Bite nose of your human leave fur on owners clothes so scratch my tummy actually i hate you now fight me blow up sofa in 3 seconds.</p>
	`,
    dueDate: 1742795479,
    assignees: [
        {
            id: '2',
            name: 'Jeremiah Minsk',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-2.jpg',
        },
        {
            id: '3',
            name: 'Max Alexander',
            email: '<EMAIL>',
            img: '/img/avatars/thumb-3.jpg',
        },
    ],
    labels: [
        {
            id: '1',
            title: 'Bug',
        },
        {
            id: '2',
            title: 'Live issue',
        },
    ],
    comments: [
        {
            id: 'Wx8FDSsVTg',
            name: 'Arlene Pierce',
            src: '/img/avatars/thumb-6.jpg',
            message:
                'Helvetica 8-bit photo booth tumblr food truck. Enamel pin wolf tousled sartorial, brunch shoreditch skateboard beard helvetica. Plaid typewriter gastropub bespoke.',
            date: new Date(y, 4, 20),
        },
        {
            id: '3AhkqqSFFr',
            name: 'Roberta Horton',
            src: '/img/avatars/thumb-7.jpg',
            message:
                '<strong>@Angelina</strong> One of the main causes of the fall of the Roman Empire was that-lacking zero-they had no way to indicate successful termination of their C programs. ',
            date: new Date(y, 4, 20),
        },
    ],
    attachments: [
        {
            id: 'jubuK7XGp3',
            name: 'mail.jpg',
            src: '/img/others/img-13.jpg',
            size: '36.1kb',
        },
        {
            id: 'NjHJhHeWrG',
            name: 'issue.jpg',
            src: '/img/others/img-16.jpg',
            size: '46.1kb',
        },
    ],
    activity: [
        {
            type: 'UPDATE-TICKET',
            dateTime: 1646580000,
            ticket: 'PD-127',
            status: 1,
            userName: 'Angelina Gotelli',
            userImg: '',
        },
        {
            type: 'COMMENT',
            dateTime: 1646578417,
            userName: 'Arlene Pierce',
            userImg: '/img/avatars/thumb-1.jpg',
            comment: `Helvetica 8-bit photo booth tumblr food truck. Enamel pin wolf tousled sartorial, brunch shoreditch skateboard beard helvetica. Plaid typewriter gastropub bespoke.`,
        },
        {
            type: 'ADD-TAGS-TO-TICKET',
            dateTime: 1646574027,
            userName: 'Eugene Stewart',
            tags: ['Live Issue', 'Bug'],
        },
        {
            type: 'ADD-FILES-TO-TICKET',
            dateTime: 1646569123,
            userName: 'Shannon Baker',
            files: ['document.csv'],
            ticket: 'PD-1092',
        },
        {
            type: 'COMMENT-MENTION',
            dateTime: 1646565473,
            userName: 'Roberta Horton',
            userImg: '/img/avatars/thumb-7.jpg',
            comment: `<strong>@Angelina</strong> One of the main causes of the fall of the Roman Empire was that-lacking zero-they had no way to indicate successful termination of their C programs. `,
        },
        {
            type: 'ASSIGN-TICKET',
            dateTime: 1646554397,
            userName: 'Lee Wheeler',
            assignee: 'Alvin Moreno',
            ticket: 'PD-1092',
        },
    ],
}

export const scrumboardData = {
    'To Do': toBeProcessCards,
    'In Progress': processingCards,
    'To Review': submittedCards,
    Completed: completedCards,
}

export const taskBugFix = [
    {
        id: '0a2ff03d-1b61-4ab0-aa43-e5c7f4578a79',
        name: 'Unable to upload file',
        dueDate: '2024-08-05T00:00:00.000Z',
        checked: false,
        progress: 'In Progress',
        priority: 'High',
        assignee: {
            name: 'Jessica Wells',
            img: '/img/avatars/thumb-8.jpg',
        },
    },
    {
        id: 'da1d38c9-e7ad-4d7d-88d4-bc88e152d10e',
        name: 'Error in database query',
        dueDate: '2024-07-15T00:00:00.000Z',
        checked: true,
        progress: 'Completed',
        priority: 'Medium',
        assignee: {
            name: 'Jeremiah Minsk',
            img: '/img/avatars/thumb-2.jpg',
        },
    },
    {
        id: 'cd820d94-aa38-40f0-97ab-2a5c82f3c701',
        name: 'Authentication problem',
        dueDate: '2024-09-20T00:00:00.000Z',
        checked: false,
        progress: 'In Progress',
        priority: 'High',
        assignee: {
            name: 'Earl Miles',
            img: '/img/avatars/thumb-10.jpg',
        },
    },
    {
        id: 'f5bfcff3-975c-4b22-a49b-1eeb8a3c03ec',
        name: 'Bug in search functionality',
        dueDate: '2024-09-05T00:00:00.000Z',
        checked: false,
        progress: 'In Progress',
        priority: 'High',
        assignee: {
            name: 'Max Alexander',
            img: '/img/avatars/thumb-3.jpg',
        },
    },
    {
        id: 'c773847d-7f60-4d27-b3b0-6f61915e9b1a',
        name: 'Compatibility issue with Firefox',
        dueDate: '2024-07-25T00:00:00.000Z',
        checked: true,
        progress: 'Completed',
        priority: 'Medium',
        assignee: {
            name: 'Angelina Gotelli',
            img: '/img/avatars/thumb-1.jpg',
        },
    },
]

export const taskDevelopment = [
    {
        id: '9ff33d5e-2f1c-4b20-b8ae-2241ed9cc624',
        name: 'Performance optimization',
        dueDate: '2024-08-30T00:00:00.000Z',
        checked: false,
        progress: 'Pending',
        priority: 'Medium',
        assignee: {
            name: 'Jeremiah Minsk',
            img: '/img/avatars/thumb-2.jpg',
        },
    },
    {
        id: 'a6951cbb-fb0d-4223-b73a-8b8b9e40f0d2',
        name: 'Payment gateway integration',
        dueDate: '2024-10-15T00:00:00.000Z',
        checked: false,
        progress: 'Pending',
        priority: 'Low',
        assignee: {
            name: 'Jeremiah Minsk',
            img: '/img/avatars/thumb-2.jpg',
        },
    },
    {
        id: 'b671d721-4d5e-4b63-8827-739e8d5cb22c',
        name: 'Update user profile page layout',
        dueDate: '2024-08-10T00:00:00.000Z',
        checked: false,
        progress: 'In Progress',
        priority: 'High',
        assignee: {
            name: 'Max Alexander',
            img: '/img/avatars/thumb-3.jpg',
        },
    },
    {
        id: 'f4d29527-84e6-4702-92d6-805b6a703dc8',
        name: 'Enhance security measures',
        dueDate: '2024-08-20T00:00:00.000Z',
        checked: false,
        progress: 'Pending',
        priority: 'Medium',
        assignee: {
            name: 'Arlene Pierce',
            img: '/img/avatars/thumb-6.jpg',
        },
    },
]

export const taskUiUx = [
    {
        id: 'b8d49ba2-ae0e-4567-aa82-ef057f0a2d2b',
        name: 'UI Layout Adjustment for Dashboard',
        dueDate: '2024-09-25T00:00:00.000Z',
        checked: false,
        progress: 'In Progress',
        priority: 'High',
        assignee: {
            name: 'Jeremiah Minsk',
            img: '/img/avatars/thumb-2.jpg',
        },
    },
    {
        id: 'fffb790d-fc52-4df6-8403-07128cc6fb31',
        name: 'UX Improvement for Onboarding Process',
        dueDate: '2024-08-15T00:00:00.000Z',
        checked: false,
        progress: 'Pending',
        priority: 'Medium',
        assignee: {
            name: 'Arlene Pierce',
            img: '/img/avatars/thumb-6.jpg',
        },
    },
    {
        id: 'b32d5d2b-f762-426f-9bbd-ec15c879e1a5',
        name: 'UI Element Styling for Product Page',
        dueDate: '2024-10-05T00:00:00.000Z',
        checked: false,
        progress: 'Pending',
        priority: 'Low',
        assignee: {
            name: 'Angelina Gotelli',
            img: '/img/avatars/thumb-1.jpg',
        },
    },
]

export const taskPlaning = [
    {
        id: '8e00e8d5-b87e-4c97-8d80-695ff91f50b0',
        name: 'Strategic Project Roadmap Planning',
        dueDate: '2024-09-30T00:00:00.000Z',
        checked: false,
        progress: 'In Progress',
        priority: 'High',
        assignee: {
            name: 'Jeremiah Minsk',
            img: '/img/avatars/thumb-2.jpg',
        },
    },
    {
        id: 'a1a1d440-8f4c-4be4-a92b-249107fd4e1d',
        name: 'Quarterly Resource Allocation Plan',
        dueDate: '2024-08-20T00:00:00.000Z',
        checked: false,
        progress: 'Pending',
        priority: 'Medium',
        assignee: {
            name: 'Arlene Pierce',
            img: '/img/avatars/thumb-6.jpg',
        },
    },
    {
        id: 'c18b5a49-43f1-4dd3-bf5a-3f4e5e4b3db2',
        name: 'Strategic Business Planning Session',
        dueDate: '2024-10-10T00:00:00.000Z',
        checked: false,
        progress: 'Pending',
        priority: 'Low',
        assignee: {
            name: 'Angelina Gotelli',
            img: '/img/avatars/thumb-1.jpg',
        },
    },
]

export const tasksData = {
    'Bug fix': taskBugFix,
    Development: taskDevelopment,
    'UI/UX': taskUiUx,
    Planing: taskPlaning,
}
