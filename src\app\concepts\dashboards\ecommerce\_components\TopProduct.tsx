'use client'

import Card from '@/components/ui/Card'
import Avatar from '@/components/ui/Avatar'
import { TbTrendingUp, TbTrendingDown } from 'react-icons/tb'

interface TopProductProps {
    data: Array<{
        id: string
        name: string
        productCode: string
        img: string
        sales: number
        growShrink: number
    }>
}

const TopProduct = ({ data }: TopProductProps) => {
    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('en-US').format(value)
    }

    const renderGrowthIndicator = (growShrink: number) => {
        const isPositive = growShrink > 0
        return (
            <div className={`flex items-center gap-1 ${isPositive ? 'text-emerald-500' : 'text-red-500'}`}>
                {isPositive ? <TbTrendingUp className="w-3 h-3" /> : <TbTrendingDown className="w-3 h-3" />}
                <span className="text-xs font-medium">{Math.abs(growShrink)}%</span>
            </div>
        )
    }

    const sortedData = data?.sort((a, b) => b.sales - a.sales) || []

    return (
        <Card>
            <h5 className="mb-6">Top Products</h5>
            <div className="space-y-4">
                {sortedData.slice(0, 5).map((product) => (
                    <div key={product.id} className="flex items-center gap-3">
                        <div className="flex-shrink-0">
                            <Avatar
                                src={product.img}
                                size="md"
                                shape="round"
                                className="border border-gray-200"
                            />
                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">
                                {product.name}
                            </div>
                            <div className="text-xs text-gray-500">
                                {product.productCode}
                            </div>
                        </div>
                        <div className="text-right">
                            <div className="font-semibold text-sm">
                                {formatNumber(product.sales)}
                            </div>
                            {renderGrowthIndicator(product.growShrink)}
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500 text-center">
                    Based on sales volume
                </div>
            </div>
        </Card>
    )
}

export default TopProduct
