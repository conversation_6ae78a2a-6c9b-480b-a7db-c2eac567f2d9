'use client'

import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import { PiCreditCardDuotone, PiReceiptDuotone, PiDownloadDuotone } from 'react-icons/pi'

const BillingTab = () => {
    return (
        <div className="space-y-6">
            {/* Current Plan */}
            <Card>
                <h5 className="font-bold text-lg mb-4">Current Plan</h5>
                <div className="flex justify-between items-start mb-4">
                    <div>
                        <h6 className="text-xl font-semibold">Growth Plan</h6>
                        <p className="text-gray-500">Perfect for growing compliance teams</p>
                        <div className="mt-2">
                            <StatusBadge className="bg-emerald-100 text-emerald-600 border border-emerald-200">
                                Active
                            </StatusBadge>
                        </div>
                    </div>
                    <div className="text-right">
                        <div className="text-2xl font-bold">$99</div>
                        <div className="text-sm text-gray-500">per month</div>
                    </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        <div className="text-lg font-semibold">5</div>
                        <div className="text-sm text-gray-500">Frameworks</div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        <div className="text-lg font-semibold">10</div>
                        <div className="text-sm text-gray-500">Team Members</div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        <div className="text-lg font-semibold">10 GB</div>
                        <div className="text-sm text-gray-500">Storage</div>
                    </div>
                </div>

                <div className="flex gap-2">
                    <Button variant="solid" className="bg-blue-500 hover:bg-blue-600">
                        Upgrade Plan
                    </Button>
                    <Button variant="default">
                        Change Plan
                    </Button>
                </div>
            </Card>

            {/* Payment Method */}
            <Card>
                <h5 className="font-bold text-lg mb-4">Payment Method</h5>
                <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div className="flex items-center">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-3">
                            <PiCreditCardDuotone className="text-xl text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <div className="font-medium">•••• •••• •••• 4242</div>
                            <div className="text-sm text-gray-500">Expires 12/25</div>
                        </div>
                    </div>
                    <Button size="sm" variant="default">Update</Button>
                </div>
            </Card>

            {/* Billing History */}
            <Card>
                <div className="flex justify-between items-center mb-4">
                    <h5 className="font-bold text-lg">Billing History</h5>
                    <Button size="sm" variant="plain">View All</Button>
                </div>
                
                <div className="space-y-3">
                    <div className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700">
                        <div className="flex items-center">
                            <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg mr-3">
                                <PiReceiptDuotone className="text-lg text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <div>
                                <div className="font-medium">Growth Plan - December 2023</div>
                                <div className="text-sm text-gray-500">Paid on Dec 15, 2023</div>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="font-medium">$99.00</span>
                            <Button size="xs" variant="plain" icon={<PiDownloadDuotone />} />
                        </div>
                    </div>
                    
                    <div className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700">
                        <div className="flex items-center">
                            <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg mr-3">
                                <PiReceiptDuotone className="text-lg text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <div>
                                <div className="font-medium">Growth Plan - November 2023</div>
                                <div className="text-sm text-gray-500">Paid on Nov 15, 2023</div>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="font-medium">$99.00</span>
                            <Button size="xs" variant="plain" icon={<PiDownloadDuotone />} />
                        </div>
                    </div>
                    
                    <div className="flex items-center justify-between py-3">
                        <div className="flex items-center">
                            <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg mr-3">
                                <PiReceiptDuotone className="text-lg text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <div>
                                <div className="font-medium">Growth Plan - October 2023</div>
                                <div className="text-sm text-gray-500">Paid on Oct 15, 2023</div>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="font-medium">$99.00</span>
                            <Button size="xs" variant="plain" icon={<PiDownloadDuotone />} />
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default BillingTab
