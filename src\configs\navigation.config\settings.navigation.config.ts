import {
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_TITLE,
} from '@/constants/navigation.constant'
import { ADMIN, USER } from '@/constants/roles.constant'
import type { NavigationTree } from '@/@types/navigation'

const settingsNavigationConfig: NavigationTree[] = [
    {
        key: 'settingsSection',
        path: '',
        title: 'Settings',
        translateKey: 'Settings',
        icon: 'settings',
        type: NAV_ITEM_TYPE_TITLE,
        authority: [ADMIN, USER],
        subMenu: [
            {
                key: 'settings',
                path: '/settings',
                title: 'Settings',
                translateKey: 'Settings',
                icon: 'settings',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
]

export default settingsNavigationConfig
